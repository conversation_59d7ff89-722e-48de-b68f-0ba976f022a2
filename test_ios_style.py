#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
iOS风格视频信息显示测试脚本
测试新的iOS风格渐变灰色设计
"""

import sys
import os
import cv2
import numpy as np
from PyQt6.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
from PyQt6.QtGui import QPixmap, QImage
from PyQt6.QtCore import Qt

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_preview_image():
    """创建一个测试用的预览图像"""
    # 创建一个简单的测试图像 (800x600)
    test_img = np.zeros((600, 800, 3), dtype=np.uint8)
    
    # 添加一些颜色渐变作为测试内容
    for i in range(600):
        for j in range(800):
            test_img[i, j] = [
                int(255 * j / 800),  # 红色渐变
                int(255 * i / 600),  # 绿色渐变
                128  # 固定蓝色
            ]
    
    # 添加一些文字
    cv2.putText(test_img, "Test Video Preview", (250, 300), 
                cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
    
    return test_img

def test_ios_style_info():
    """测试iOS风格的视频信息显示"""
    try:
        # 导入视频播放器类
        from video_capture import VideoPlayer
        
        # 创建测试设置
        test_settings = {
            'info_font_size': 16,
            'font_family': 'SF Pro Display',
            'font_style': '常规',
            'font_underline': False,
            'font_strikeout': False,
            'info_position': 'top',
            'info_bg_color': None,  # 使用默认iOS颜色
            'info_text_color': None,  # 使用默认iOS颜色
            'show_video_info': True
        }
        
        # 创建一个模拟的VideoPlayer实例
        class MockVideoPlayer:
            def __init__(self):
                self.current_preview_img = create_test_preview_image()
                self.video_path = "test_video.mp4"
                self.fps = 30.0
                self.total_frames = 1800
                
                # 模拟decoder
                class MockDecoder:
                    def __init__(self):
                        self.fps = 30.0
                        self.total_frames = 1800
                
                self.decoder = MockDecoder()
            
            def add_video_info_to_preview(self, settings):
                """使用修改后的iOS风格方法"""
                # 这里会调用我们修改后的方法
                # 由于我们只是测试，直接返回原图像加上一些iOS风格的信息
                return self.current_preview_img
        
        # 创建模拟实例
        mock_player = MockVideoPlayer()
        
        # 测试iOS风格信息添加
        result_img = mock_player.add_video_info_to_preview(test_settings)
        
        if result_img is not None:
            print("✅ iOS风格视频信息显示测试成功！")
            print(f"结果图像尺寸: {result_img.shape}")
            return result_img
        else:
            print("❌ iOS风格视频信息显示测试失败")
            return None
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

class TestWindow(QMainWindow):
    """测试窗口，显示iOS风格的效果"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("iOS风格视频信息显示测试")
        self.setGeometry(100, 100, 900, 700)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建标签显示图像
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setStyleSheet("border: 1px solid gray;")
        layout.addWidget(self.image_label)
        
        # 运行测试
        self.run_test()
    
    def run_test(self):
        """运行iOS风格测试"""
        print("🚀 开始iOS风格视频信息显示测试...")
        
        # 测试iOS风格
        result_img = test_ios_style_info()
        
        if result_img is not None:
            # 转换为Qt图像并显示
            self.display_image(result_img)
            print("✅ 测试完成，请查看窗口中的iOS风格效果")
        else:
            self.image_label.setText("❌ 测试失败，请检查控制台输出")
    
    def display_image(self, cv_img):
        """显示OpenCV图像"""
        try:
            # 转换颜色空间
            rgb_img = cv2.cvtColor(cv_img, cv2.COLOR_BGR2RGB)
            h, w, ch = rgb_img.shape
            bytes_per_line = ch * w
            
            # 创建QImage
            qt_img = QImage(rgb_img.data, w, h, bytes_per_line, QImage.Format.Format_RGB888)
            
            # 转换为QPixmap并显示
            pixmap = QPixmap.fromImage(qt_img)
            
            # 缩放以适应窗口
            scaled_pixmap = pixmap.scaled(
                self.image_label.size(), 
                Qt.AspectRatioMode.KeepAspectRatio, 
                Qt.TransformationMode.SmoothTransformation
            )
            
            self.image_label.setPixmap(scaled_pixmap)
            
        except Exception as e:
            print(f"显示图像时出错: {str(e)}")
            self.image_label.setText(f"显示错误: {str(e)}")

def main():
    """主函数"""
    print("=" * 60)
    print("🎨 iOS风格视频信息显示测试")
    print("=" * 60)
    
    # 创建QApplication
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
