#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
iOS风格视频信息显示效果展示
"""

import sys
import os
import cv2
import numpy as np
from PyQt6.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PyQt6.QtGui import QPixmap, QImage
from PyQt6.QtCore import Qt

def create_demo_image():
    """创建一个演示用的预览图像"""
    # 创建一个渐变背景图像 (1200x800)
    demo_img = np.zeros((800, 1200, 3), dtype=np.uint8)
    
    # 创建彩色渐变背景
    for i in range(800):
        for j in range(1200):
            demo_img[i, j] = [
                int(100 + 155 * j / 1200),  # 红色渐变
                int(150 + 105 * i / 800),   # 绿色渐变
                int(200 - 50 * j / 1200)    # 蓝色渐变
            ]
    
    # 添加一些装饰性元素
    cv2.circle(demo_img, (300, 200), 80, (255, 255, 255), -1)
    cv2.circle(demo_img, (900, 600), 60, (200, 200, 255), -1)
    cv2.rectangle(demo_img, (500, 300), (700, 500), (255, 200, 200), -1)
    
    # 添加标题文字
    cv2.putText(demo_img, "iOS Style Video Info Demo", (350, 400), 
                cv2.FONT_HERSHEY_SIMPLEX, 1.5, (255, 255, 255), 3)
    
    return demo_img

def test_ios_style():
    """测试iOS风格效果"""
    try:
        # 导入视频播放器类
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        from video_capture import VideoPlayer
        
        # 创建测试设置
        settings = {
            'info_font_size': 16,
            'font_family': 'Microsoft YaHei UI',
            'font_style': '常规',
            'font_underline': False,
            'font_strikeout': False,
            'info_position': 'top',
            'info_bg_color': None,
            'info_text_color': None,
            'show_video_info': True
        }
        
        # 创建测试用的VideoPlayer实例
        class DemoVideoPlayer(VideoPlayer):
            def __init__(self):
                # 只设置必要的属性，不调用父类初始化
                self.current_preview_img = create_demo_image()
                self.video_path = "demo_video.mp4"
                self.fps = 25.0
                self.total_frames = 1500
                
                # 模拟decoder
                class MockDecoder:
                    def __init__(self):
                        self.fps = 25.0
                        self.total_frames = 1500
                
                self.decoder = MockDecoder()
        
        # 创建演示实例
        demo_player = DemoVideoPlayer()
        
        # 应用iOS风格信息
        result_img = demo_player.add_video_info_to_preview(settings)
        
        return result_img
        
    except Exception as e:
        print(f"测试iOS风格时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

class DemoWindow(QMainWindow):
    """演示窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("iOS风格视频信息显示效果")
        self.setGeometry(100, 100, 1300, 900)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加说明标签
        info_label = QLabel("iOS风格视频信息显示效果演示")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(info_label)
        
        # 创建图像显示标签
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setStyleSheet("border: 2px solid #ccc; background-color: #f5f5f5;")
        self.image_label.setMinimumSize(1200, 800)
        layout.addWidget(self.image_label)
        
        # 创建按钮布局
        button_layout = QHBoxLayout()
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新效果")
        refresh_btn.clicked.connect(self.refresh_demo)
        refresh_btn.setStyleSheet("padding: 8px 16px; font-size: 14px;")
        button_layout.addWidget(refresh_btn)
        
        # 保存按钮
        save_btn = QPushButton("保存图像")
        save_btn.clicked.connect(self.save_image)
        save_btn.setStyleSheet("padding: 8px 16px; font-size: 14px;")
        button_layout.addWidget(save_btn)
        
        layout.addLayout(button_layout)
        
        # 初始化显示
        self.current_image = None
        self.refresh_demo()
    
    def refresh_demo(self):
        """刷新演示效果"""
        print("🎨 生成iOS风格效果...")
        
        # 测试iOS风格
        result_img = test_ios_style()
        
        if result_img is not None:
            self.current_image = result_img
            self.display_image(result_img)
            print("✅ iOS风格效果生成成功！")
        else:
            self.image_label.setText("❌ 生成失败，请检查控制台输出")
    
    def save_image(self):
        """保存当前图像"""
        if self.current_image is not None:
            filename = "ios_style_demo.png"
            cv2.imwrite(filename, self.current_image)
            print(f"✅ 图像已保存为: {filename}")
        else:
            print("❌ 没有可保存的图像")
    
    def display_image(self, cv_img):
        """显示OpenCV图像"""
        try:
            # 转换颜色空间
            rgb_img = cv2.cvtColor(cv_img, cv2.COLOR_BGR2RGB)
            h, w, ch = rgb_img.shape
            bytes_per_line = ch * w
            
            # 创建QImage
            qt_img = QImage(rgb_img.data, w, h, bytes_per_line, QImage.Format.Format_RGB888)
            
            # 转换为QPixmap
            pixmap = QPixmap.fromImage(qt_img)
            
            # 缩放以适应标签大小
            label_size = self.image_label.size()
            scaled_pixmap = pixmap.scaled(
                label_size, 
                Qt.AspectRatioMode.KeepAspectRatio, 
                Qt.TransformationMode.SmoothTransformation
            )
            
            self.image_label.setPixmap(scaled_pixmap)
            
        except Exception as e:
            print(f"显示图像时出错: {str(e)}")
            self.image_label.setText(f"显示错误: {str(e)}")

def main():
    """主函数"""
    print("=" * 60)
    print("🍎 iOS风格视频信息显示效果演示")
    print("=" * 60)
    
    # 创建QApplication
    app = QApplication(sys.argv)
    
    # 创建演示窗口
    window = DemoWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
