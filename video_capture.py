import sys
import av
from av import VideoFrame
import warnings
from concurrent.futures import ThreadPoolExecutor
warnings.filterwarnings("ignore")  # 忽略PyAV警告
import cv2
from PyQt6.QtWidgets import (QApplication, QMainWindow, QLabel, QSlider, 
                           QPushButton, QVBoxLayout, QHBoxLayout, QWidget, QComboBox, QSpinBox,
                           QFileDialog, QLineEdit, QDoubleSpinBox, QMessageBox, QProgressDialog,
                           QStyle, QCheckBox, QListWidget, QListWidgetItem, QProgressBar, QGroupBox, QGridLayout,
                           QSizePolicy, QDialog, QFormLayout, QMenu, QScrollArea, QGraphicsDropShadowEffect,
                           QDialogButtonBox, QFontComboBox)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer, QTime, QDate, QFileInfo, QDir, QFile, QEvent, QPointF
from PyQt6.QtGui import QImage, QPixmap, QDragEnterEvent, QDropEvent, QKeySequence, QShortcut, QPainter, QColor, QPen, QFont, QLinearGradient, QPainterPath
import os
import imageio
from PIL import Image
import numpy as np
from io import BytesIO

def render_text_with_qt(text, font, text_color, bg_color, width=800, height=200):
    """使用Qt渲染文本并返回PIL图像
    
    这个函数使用Qt的渲染机制来绘制文本，然后将结果转换为PIL图像
    这样可以利用Qt的自动字体回退机制来正确显示中文
    """
    from PyQt6.QtGui import QFont, QColor, QImage, QPainter, QFontMetrics
    from PyQt6.QtCore import Qt, QRect, QSize
    
    # 创建QImage作为绘图表面
    image = QImage(width, height, QImage.Format.Format_ARGB32)
    image.fill(bg_color)
    
    # 创建QPainter
    painter = QPainter(image)
    try:
        # 尝试设置渲染提示
        painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)
        painter.setRenderHint(QPainter.RenderHint.TextAntialiasing, True)
        painter.setRenderHint(QPainter.RenderHint.SmoothPixmapTransform, True)
    except:
        # 如果失败，使用兼容性写法
        painter.setRenderHint(QPainter.Antialiasing, True)
        painter.setRenderHint(QPainter.TextAntialiasing, True)
        painter.setRenderHint(QPainter.SmoothPixmapTransform, True)
    
    # 确保字体有抗锯齿和提示首选项
    font.setStyleStrategy(QFont.StyleStrategy.PreferAntialias)
    font.setHintingPreference(QFont.HintingPreference.PreferFullHinting)
    
    # 设置字体和颜色 - 直接使用原始字体，让Qt的提示和抗锯齿机制自动处理
    painter.setFont(font)
    painter.setPen(text_color)
    
    # 计算文本位置以居中显示
    font_metrics = QFontMetrics(font)
    try:
        text_width = font_metrics.horizontalAdvance(text)
    except AttributeError:
        # 兼容旧版Qt
        text_width = font_metrics.width(text)
    
    text_height = font_metrics.height()
    x = (width - text_width) // 2
    y = (height - text_height) // 2 + font_metrics.ascent()
    
    # 绘制文本
    painter.drawText(x, y, text)
    painter.end()
    
    # 转换为PIL图像
    bits = image.constBits()
    try:
        # PyQt6
        arr = np.array(bits).reshape(height, width, 4)
    except:
        # 兼容性处理
        bits.setsize(height * width * 4)
        arr = np.frombuffer(bits, np.uint8).reshape(height, width, 4)
    
    # BGRA到RGBA的转换
    arr = arr[:, :, [2, 1, 0, 3]]
    
    # 创建PIL图像
    pil_img = Image.fromarray(arr)
    return pil_img

class CustomFontPreview(QWidget):
    """自定义字体预览控件，提供更高质量的字体渲染"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.text = "AaBbYyZz 你好，世界！"
        self.font = QFont()
        self.text_color = QColor(0, 0, 0)
        self.bg_color = QColor(255, 255, 255)
        self.setMinimumHeight(150)
    
    def setText(self, text):
        self.text = text
        self.update()
    
    def setFont(self, font):
        self.font = font
        self.update()
    
    def setColors(self, text_color, bg_color):
        self.text_color = text_color
        self.bg_color = bg_color
        self.update()
    
    def paintEvent(self, event):
        painter = QPainter(self)
        try:
            # 尝试设置渲染提示
            painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)
            painter.setRenderHint(QPainter.RenderHint.TextAntialiasing, True)
            painter.setRenderHint(QPainter.RenderHint.SmoothPixmapTransform, True)
        except:
            # 如果失败，使用兼容性写法
            painter.setRenderHint(QPainter.Antialiasing, True)
            painter.setRenderHint(QPainter.TextAntialiasing, True)
            painter.setRenderHint(QPainter.SmoothPixmapTransform, True)
        
        # 绘制背景
        painter.fillRect(self.rect(), self.bg_color)
        
        # 设置字体和颜色 - 直接使用原始字体，让Qt自动处理
        painter.setFont(self.font)
        painter.setPen(self.text_color)
        
        # 居中绘制文本
        try:
            painter.drawText(self.rect(), Qt.AlignmentFlag.AlignCenter, self.text)
        except:
            # 兼容性写法
            painter.drawText(self.rect(), Qt.AlignCenter, self.text)

class LeftAlignedFontPreview(CustomFontPreview):
    """靠左对齐的文本预览组件，继承自CustomFontPreview"""
    def __init__(self, parent=None, left_margin=10):
        super().__init__(parent)
        self.left_margin = left_margin
    
    def paintEvent(self, event):
        from PyQt6.QtGui import QPainter
        from PyQt6.QtCore import Qt, QRect
        
        painter = QPainter(self)
        try:
            # 尝试设置渲染提示
            painter.setRenderHint(QPainter.RenderHint.Antialiasing, True)
            painter.setRenderHint(QPainter.RenderHint.TextAntialiasing, True)
            painter.setRenderHint(QPainter.RenderHint.SmoothPixmapTransform, True)
        except:
            # 如果失败，使用兼容性写法
            painter.setRenderHint(QPainter.Antialiasing, True)
            painter.setRenderHint(QPainter.TextAntialiasing, True)
            painter.setRenderHint(QPainter.SmoothPixmapTransform, True)
        
        # 不绘制背景，保持透明，这样不会覆盖父组件的渐变背景
        # painter.fillRect(self.rect(), self.bg_color)
        
        # 设置字体和颜色
        painter.setFont(self.font)
        painter.setPen(self.text_color)
        
        # 靠左绘制文本，垂直居中
        text_rect = QRect(self.left_margin, 0, self.width() - self.left_margin * 2, self.height())
        try:
            painter.drawText(text_rect, Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter, self.text)
        except:
            # 兼容性写法
            painter.drawText(text_rect, Qt.AlignLeft | Qt.AlignVCenter, self.text)

# 检测文本是否包含中文字符
def contains_chinese(text):
    """检测文本是否包含中文字符"""
    if not text or not isinstance(text, str):
        return False
    
    # 检测文本中的中文字符（简体中文和繁体中文的Unicode范围）
    for ch in text:
        if '\u4e00' <= ch <= '\u9fff':
            return True
    return False

# 无需3D文本效果

# 简化的文本绘制函数
def draw_text_with_mixed_font(draw, pos, text, fill, font, chinese_font=None, settings=None, line_type=None):
    """简化的文本绘制函数，支持字体样式设置
    
    在PIL中，我们不需要像Qt那样处理自动字体回退，因为PIL不支持这个功能
    但我们可以简化函数，只使用主字体，类似于Qt的处理方式
    """
    # 字体垂直偏移设置已移除，现在使用Qt的自动居中文本
    
    # 直接使用主字体，不再区分中英文
    # 绘制文本
    draw.text((pos[0], pos[1]), text, fill=fill, font=font)
    
    # 计算文本总宽度（用于绘制下划线和删除线）
    try:
        text_width = font.getbbox(text)[2]
    except AttributeError:
        try:
            text_width = font.getsize(text)[0]
        except:
            # 如果无法获取宽度，尝试使用另一种方法
            text_width = len(text) * font.size
    
    # 如果设置了下划线，绘制下划线
    if settings and settings.get('font_underline', False):
        line_y = pos[1] + font.size - 2  # 下划线位置
        draw.line([(pos[0], line_y), (pos[0] + text_width, line_y)], fill=fill, width=1)
    
    # 如果设置了删除线，绘制删除线
    if settings and settings.get('font_strikeout', False):
        line_y = pos[1] + font.size / 2  # 删除线位置
        draw.line([(pos[0], line_y), (pos[0] + text_width, line_y)], fill=fill, width=1)
import numpy as np
import json
import re
import shutil
from pathlib import Path
import locale
import ctypes
from ctypes import wintypes
import winreg
import subprocess
import tempfile
import time
import multiprocessing
import av
from threading import Thread, Lock
from collections import OrderedDict
import traceback
import uuid
import hashlib

if hasattr(sys, '_MEIPASS'):
    # 如果是打包后的环境，使用 spawn 方法
    multiprocessing.set_start_method('spawn', force=True)
else:
    # 如果是开发环境，使用默认方法
    try:
        multiprocessing.set_start_method('spawn')
    except RuntimeError:
        pass

# 添加 FFmpeg 路径处理函数
def get_ffmpeg_path():
    """获取 FFmpeg 可执行文件路径"""
    try:
        # 获取可执行文件所在目录
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller 打包后的路径
            base_path = sys._MEIPASS
        else:
            # 开发环境下的路径
            base_path = os.path.dirname(os.path.abspath(__file__))
        
        # 尝试多个可能的位置
        possible_paths = [
            os.path.join(base_path, 'ffmpeg.exe'),  # 同目录
            os.path.join(os.path.dirname(base_path), 'ffmpeg.exe'),  # 上级目录
            os.path.join(os.path.dirname(sys.executable), 'ffmpeg.exe'),  # exe所在目录
            'ffmpeg.exe'  # 当前工作目录
        ]
        
        # 遍历所有可能的路径
        for ffmpeg_path in possible_paths:
            if os.path.exists(ffmpeg_path):
                return ffmpeg_path
                
        # 如果都找不到，抛出异常
        raise Exception("找不到 FFmpeg，请确保 FFmpeg 与程序在同一目录下")
        
    except Exception as e:
        raise

def process_animation(params, progress_queue, result_queue):
    """修改动画处理函数以使用新的解码器"""
    try:
        # 获取 FFmpeg 路径
        ffmpeg_path = get_ffmpeg_path()
        
        progress_queue.put((0, "正在准备处理..."))
        
        # 使用传入的解码器
        decoder = params['decoder']
        if not decoder:
            raise Exception("解码器未初始化")
        
        # 构建 FFmpeg 命令
        start_time = params['start_frame'] / decoder.fps
        
        filters = []
        filters.append(f'fps={params["target_fps"]}')
        
        if params['resize']:
            target_width = params['target_width']
            target_height = params['target_height']
            
            if target_width > 0 and target_height > 0:
                filters.append(f'scale={target_width}:{target_height}')

        # 根据格式类型构建不同的命令
        ffmpeg_cmd = [
            ffmpeg_path, '-y',
            '-ss', str(start_time),
            '-t', str(params['duration']),
            '-i', params['video_path'],
            '-vf', ','.join(filters),
        ]

        # 添加格式特定的参数
        if params['format_type'] == 'gif':
            # GIF 特定参数
            if params['quality_mode'] == "完全无损":
                # ... GIF 完全无损模式的参数 ...
                pass
            elif params['quality_mode'] == "平衡":
                # ... GIF 平衡模式的参数 ...
                pass
            else:  # 快速模式
                # ... GIF 快速模式的参数 ...
                pass
        else:  # webp
            # WebP 特定参数
            if params['quality_mode'] == "快速":
                # ... WebP 快速模式的参数 ...
                pass
            elif params['quality_mode'] == "平衡":
                # ... WebP 平衡模式的参数 ...
                pass
            else:  # 完全无损
                # ... WebP 完全无损模式的参数 ...
                pass

        # 添加循环设置
        if params['loop_count'] == 0:
            ffmpeg_cmd.extend(['-loop', '0'])
        else:
            ffmpeg_cmd.extend(['-loop', str(params['loop_count'])])

        # 添加输出格式和路径
        ffmpeg_cmd.extend(['-f', params['format_type']])
        ffmpeg_cmd.append(params['save_path'])

        # 执行 FFmpeg 命令
        process = subprocess.Popen(
            ffmpeg_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            creationflags=subprocess.CREATE_NO_WINDOW,
            encoding='utf-8'  # 添加 UTF-8 编码设置
        )
        
        # 监控进度
        while True:
            if process.poll() is not None:
                break
                
            line = process.stderr.readline()
            if line:
                if "frame=" in line:
                    try:
                        frame_match = re.search(r'frame=\s*(\d+)', line)
                        time_match = re.search(r'time=\s*(\d+):(\d+):(\d+.\d+)', line)
                        
                        if frame_match and time_match:
                            current_time = float(time_match.group(1)) * 3600 + \
                                         float(time_match.group(2)) * 60 + \
                                         float(time_match.group(3))
                            progress = min(95, int(current_time * 95 / params['duration']))
                            progress_queue.put((progress, f"正在处理: {int(current_time)}/{int(params['duration'])}秒"))
                    except Exception as e:
                        print(f"解析进度时出错: {str(e)}")
            
            time.sleep(0.1)
        
        # 获取最终输出
        stdout, stderr = process.communicate()
        if process.returncode != 0:
            raise Exception(f"FFmpeg 处理失败: {stderr}")
        
        # 检查输出文件
        if not os.path.exists(params['save_path']):
            raise Exception("输出文件创建失败")
            
        if os.path.getsize(params['save_path']) == 0:
            raise Exception("输出文件大小为0")
        
        result_data = {
            'fps': params['target_fps'],
            'duration': params['duration'],
            'file_path': params['save_path']
        }
        result_queue.put((True, "制作完成", result_data))
        
    except Exception as e:
        result_queue.put((False, str(e), {}))

# 添加线程类
class AnimationThread(QThread):
    progress_signal = pyqtSignal(tuple)  # 发送进度信息
    result_signal = pyqtSignal(tuple)    # 发送结果信息

    def __init__(self, params):
        super().__init__()
        self.params = params
        self.is_cancelled = False
        self.process = None  # 添加 process 属性

    def run(self):
        try:
            # 获取 FFmpeg 路径
            ffmpeg_path = get_ffmpeg_path()
            
            self.progress_signal.emit((0, "正在打开视频..."))
            cap = cv2.VideoCapture(self.params['video_path'])
            if not cap.isOpened():
                raise Exception("无法打开视频文件")
                
            # 添加取消检查
            if self.is_cancelled:
                cap.release()
                return

            # 计算参数
            self.progress_signal.emit((5, "正在计算参数..."))
            target_fps = min(self.params['target_fps'], cap.get(cv2.CAP_PROP_FPS))
            frame_interval = max(1, round(cap.get(cv2.CAP_PROP_FPS) / target_fps))
            total_frames = int(self.params['duration'] * target_fps)
            
            print(f"原始视频帧率: {cap.get(cv2.CAP_PROP_FPS)}")
            print(f"实际使用帧率: {target_fps}")
            print(f"帧间隔: {frame_interval}")
            print(f"需要处理的总帧数: {total_frames}")
            
            # 直接使用选择的帧位置作为起始点
            start_frame = self.params['start_frame']
            end_frame = min(start_frame + (total_frames * frame_interval), self.params['video_total_frames'])
            print(f"起始帧: {start_frame}")
            print(f"结束帧: {end_frame}")

            print("开始 WebP 处理流程")
            self.progress_signal.emit((10, "正在准备 FFmpeg 处理..."))
            
            # 构建 FFmpeg 命令
            print("构建 FFmpeg 命令...")
            start_time = self.params.get('exact_start_time', start_frame / cap.get(cv2.CAP_PROP_FPS))
            print(f"起始时间点: {start_time}秒")
            
            filters = []
            filters.append(f'fps={target_fps}')
            
            if self.params['resize']:
                target_width = self.params['target_width']
                target_height = self.params['target_height']
                if target_width > 0 and target_height > 0:
                    filters.append(f'scale={target_width}:{target_height}:flags=lanczos')
                    print(f"添加缩放滤镜: scale={target_width}:{target_height}")
            
            print(f"使用的滤镜: {','.join(filters)}")
            
            # FFmpeg 命令构建
            ffmpeg_cmd = [
                ffmpeg_path, '-y',
                '-ss', str(start_time),
                '-accurate_seek',
                '-vsync', 'cfr',
                '-start_at_zero',
                '-t', str(self.params['duration']),
                '-i', self.params['video_path'],
                '-vf', ','.join(filters),
                '-c:v', 'libwebp'
            ]

            # 根据质量模式添加不同的参数
            quality_mode = self.params['quality_mode']
            if quality_mode == "快速":
                ffmpeg_cmd.extend([
                    '-quality', '75',
                    '-compression_level', '3',
                    '-preset', 'drawing',
                    '-qmin', '10',
                    '-qmax', '50'
                ])
            elif quality_mode == "平衡":
                ffmpeg_cmd.extend([
                    '-quality', '90',
                    '-compression_level', '4',
                    '-preset', 'photo',
                    '-qmin', '5',
                    '-qmax', '30'
                ])
            elif quality_mode == "高质量":
                ffmpeg_cmd.extend([
                    '-quality', '95',
                    '-compression_level', '5',
                    '-preset', 'photo',
                    '-qmin', '1',
                    '-qmax', '10'
                ])
            else:  # 完全无损
                ffmpeg_cmd.extend([
                    '-lossless', '1',
                    '-compression_level', '6',
                    '-preset', 'photo',
                    '-quality', '100',
                    '-qmin', '0',
                    '-qmax', '0',
                    '-pix_fmt', 'rgb24',
                    '-auto-alt-ref', '0'
                ])

            # 添加循环参数
            loop_params = [
                '-loop', str(0 if self.params['loop_count'] == 0 else self.params['loop_count']),
            ]
            print(f"循环设置: {loop_params}")
            
            # 合并所有参数
            ffmpeg_cmd.extend(loop_params)
            ffmpeg_cmd.append(self.params['save_path'])
            
            print("完整的 FFmpeg 命令:")
            print(' '.join(ffmpeg_cmd))
            
            # 执行 FFmpeg 命令
            self.progress_signal.emit((30, "正在使用 FFmpeg 处理..."))
            try:
                self.process = subprocess.Popen(
                    ffmpeg_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True,
                    creationflags=subprocess.CREATE_NO_WINDOW,
                    encoding='utf-8'  # 添加 UTF-8 编码
                )
                
                print("FFmpeg 处理中...")
                
                # 读取 FFmpeg 的输出来获取真实进度
                while True:
                    # 添加取消检查
                    if self.is_cancelled:
                        if self.process:
                            self.process.terminate()
                            self.process.wait()  # 等待进程完全终止
                        return
                        
                    if self.process.poll() is not None:
                        break
                    
                    # 读取 stderr 输出
                    line = self.process.stderr.readline()
                    if line:
                        print(f"FFmpeg 输出: {line.strip()}")
                        # 尝试从输出中解析进度
                        if "frame=" in line:
                            try:
                                # 解析当前帧数和时间
                                frame_match = re.search(r'frame=\s*(\d+)', line)
                                time_match = re.search(r'time=\s*(\d+):(\d+):(\d+.\d+)', line)
                                
                                if frame_match and time_match:
                                    current_frame = int(frame_match.group(1))
                                    hours = int(time_match.group(1))
                                    minutes = int(time_match.group(2))
                                    seconds = float(time_match.group(3))
                                    
                                    # 计算总秒数和进度百分比
                                    current_time = hours * 3600 + minutes * 60 + seconds
                                    total_time = self.params['duration']
                                    progress = 30 + min(65, int(current_time * 65 / total_time))
                                    
                                    # 格式化时间显示
                                    time_str = f"{hours:02d}:{minutes:02d}:{int(seconds):02d}"
                                    self.progress_signal.emit((
                                        progress, 
                                        f"正在处理: {time_str} / {int(total_time/3600):02d}:{int((total_time%3600)/60):02d}:{int(total_time%60):02d}"
                                    ))
                            except Exception as e:
                                print(f"解析进度时出错: {str(e)}")
                    
                    # 避免 CPU 过度使用
                    time.sleep(0.1)
                
                # 获取最终输出
                stdout, stderr = self.process.communicate()
                if self.process.returncode != 0:
                    print(f"FFmpeg 错误输出: {stderr}")
                    raise Exception(f"FFmpeg 处理失败: {stderr}")
                else:
                    print("FFmpeg 处理完成")
                
            except Exception as e:
                print(f"FFmpeg 执行错误: {str(e)}")
                raise Exception(f"FFmpeg 执行错误: {str(e)}")
            
            # 验证输出文件
            print("验证输出文件...")
            self.progress_signal.emit((95, "正在验证文件..."))
            if not os.path.exists(self.params['save_path']):
                print("错误：文件未创建")
                raise Exception("文件创建失败")
            
            file_size = os.path.getsize(self.params['save_path'])
            print(f"输出文件大小: {file_size} 字节")
            if file_size == 0:
                print("错误：文件大小为0")
                raise Exception("文件大小为0")
            
            print("处理完成！")
            self.progress_signal.emit((100, "完成!"))
            
            result_data = {
                'fps': self.params['target_fps'],
                'duration': self.params['duration'],
                'file_path': self.params['save_path']
            }
            self.result_signal.emit((True, "制作完成", result_data))
            
        except Exception as e:
            # 如果是因为取消而退出，不发送错误信号
            if not self.is_cancelled:
                self.result_signal.emit((False, str(e), None))

    def cancel(self):
        """添加取消方法"""
        self.is_cancelled = True
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=2)  # 等待最多2秒
            except:
                try:
                    self.process.kill()  # 如果终止失败，强制结束进程
                except:
                    pass

class AnimationWorker(QThread):
    progress = pyqtSignal(int)  # 进度信号
    finished = pyqtSignal(bool, str, dict)  # 完成信号：成功/失败，消息，结果数据
    
    def __init__(self, params):
        super().__init__()
        self.params = params
        
    def run(self):
        try:
            # 使用独立的视频捕获对象
            cap = cv2.VideoCapture(self.params['video_path'])
            if not cap.isOpened():
                raise Exception("无法打开视频文件")

            frames = []
            total_frames = self.params['total_frames']
            
            # 计算采样间隔，确保平滑播放效果
            target_fps = self.params['target_fps']
            video_fps = cap.get(cv2.CAP_PROP_FPS)
            
            # 如果目标帧率高于视频帧率，使用视频原始帧率
            if target_fps > video_fps:
                target_fps = video_fps
                
            # 计算帧采样间隔
            frame_interval = max(1, round(video_fps / target_fps))
            total_frames = int(self.params['duration'] * target_fps)
            
            # 计算起始和结束位置
            start_frame = self.params['start_frame']
            end_frame = min(start_frame + (total_frames * frame_interval), self.params['video_total_frames'])
            
            # 预分配内存
            frames = []
            frames_needed = min(total_frames, int((end_frame - start_frame) / frame_interval))
            
            # 批量读取帧
            cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
            frame_count = 0
            frame_position = start_frame
            
            while frame_position < end_frame and frame_count < frames_needed:
                ret, frame = cap.read()
                if not ret:
                    break
                    
                # 调整大小（如果需要）
                if self.params['resize']:
                    target_width = self.params['target_width']
                    target_height = self.params['target_height']
                    
                    # 处理自适应尺寸
                    if target_width == 0 and target_height > 0:
                        ratio = frame.shape[1] / frame.shape[0]
                        target_width = int(target_height * ratio)
                    elif target_height == 0 and target_width > 0:
                        ratio = frame.shape[0] / frame.shape[1]
                        target_height = int(target_width * ratio)
                    
                    if target_width > 0 and target_height > 0:
                        frame = cv2.resize(frame, (target_width, target_height),
                                         interpolation=cv2.INTER_LANCZOS4)
                
                # 转换颜色空间并优化颜色
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
                # 添加帧
                frames.append(frame_rgb)
                frame_count += 1
                
                # 跳过帧以达到目标帧率
                for _ in range(frame_interval - 1):
                    cap.read()
                frame_position += frame_interval
                
                # 更新进度
                progress = int((frame_count * 100) / frames_needed)
                self.progress.emit(progress)
            
            cap.release()
            
            if not frames:
                raise Exception("未能收集到有效帧")
            
            # 保存动画
            try:
                if self.params['format_type'] == 'gif':
                    # 使用 PIL 创建高质量 GIF
                    pil_frames = []
                    for frame in frames:
                        # 增强色彩处理
                        img = Image.fromarray(frame)
                        # 转换为 P 模式，使用更好的调色板
                        if img.mode != 'P':
                            # 使用 Adaptive 调色板，增加颜色数量
                            img = img.convert('RGB').quantize(
                                colors=256,  # 最大颜色数
                                method=Image.Quantize.MEDIANCUT,  # 使用中位切分法
                                kmeans=1,  # 使用 K-means 优化
                                dither=Image.Dither.FLOYDSTEINBERG  # 使用 Floyd-Steinberg 抖动
                            )
                        pil_frames.append(img)
                    
                    duration = int(1000 / target_fps)  # 转换为毫秒
                    
                    # 使用优化的 GIF 保存设置
                    pil_frames[0].save(
                        self.params['save_path'],
                        format='GIF',
                        save_all=True,
                        append_images=pil_frames[1:],
                        duration=duration,
                        loop=0,  # 始终使用无限循环
                        optimize=False,  # 关闭优化以保持质量
                        disposal=2,  # 使用更好的帧处理方式
                    )
                else:  # webp
                    # 确保输出目录存在
                    os.makedirs(os.path.dirname(self.params['save_path']), exist_ok=True)
                    
                    # 转换帧为 PIL 图像
                    pil_frames = []
                    for frame in frames:
                        img = Image.fromarray(frame)
                        if img.mode != 'RGB':
                            img = img.convert('RGB')
                        pil_frames.append(img)
                    
                    # 使用 Pillow 保存 WebP
                    pil_frames[0].save(
                        self.params['save_path'],
                        format='WebP',
                        save_all=True,
                        append_images=pil_frames[1:],
                        duration=int(1000 / target_fps),
                        loop=0,  # 始终使用无限循环
                        quality=95,       # 稍微降低质量以减小文件大小
                        method=4,         # 中等压缩效率
                        lossless=False,   # 使用有损压缩
                        exact=True,       # 保持精确的帧延迟
                        minimize_size=False,  # 不过度优化大小
                    )
                
                # 验证文件
                if not os.path.exists(self.params['save_path']):
                    raise Exception("文件创建失败")
                    
                if os.path.getsize(self.params['save_path']) == 0:
                    raise Exception("文件大小为0")
                    
            except Exception as e:
                raise Exception(f"保存动画文件时出错: {str(e)}")
            
            result_data = {
                'fps': target_fps,
                'duration': self.params['duration'],
                'frame_count': len(frames),
                'actual_fps': video_fps / frame_interval
            }
            self.finished.emit(True, "制作完成", result_data)
            
        except Exception as e:
            self.finished.emit(False, str(e), {})

class ClickableSlider(QSlider):
    """优化的进度条"""
    def __init__(self, orientation):
        super().__init__(orientation)
        
        # 设置滑块追踪模式为立即更新
        self.setTracking(True)
        
        # 设置页面步长为1%的范围
        self.setPageStep(1)
        
        # 启用鼠标追踪
        self.setMouseTracking(True)
        
        # 设置进度条样式
        self.setStyleSheet("""
            QSlider {
                min-height: 24px;
                max-height: 24px;
            }
            
            QSlider::groove:horizontal {
                border: none;
                height: 6px;
                background: #E0E0E0;
                margin: 0px;
                border-radius: 3px;
            }
            
            QSlider::handle:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #3498db, stop:1 #2980b9);
                border: 2px solid #2980b9;
                width: 16px;
                height: 16px;
                margin: -5px 0;
                border-radius: 9px;
            }
            
            QSlider::handle:horizontal:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2980b9, stop:1 #2472a4);
                border: 2px solid #2472a4;
            }
            
            QSlider::handle:horizontal:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2472a4, stop:1 #1a5276);
                border: 2px solid #1a5276;
            }
            
            QSlider::sub-page:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 3px;
            }
            
            QSlider::add-page:horizontal {
                background: #E0E0E0;
                border-radius: 3px;
            }
        """)

    def mousePressEvent(self, event):
        """处理鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            # 确保位置值为整数
            pos = int(event.position().x())
            # 计算点击位置对应的值
            value = QStyle.sliderValueFromPosition(
                self.minimum(),
                self.maximum(),
                pos,
                self.width()
            )
            # 立即更新值
            self.setValue(value)
            # 确保滑块跟随鼠标
            self.setSliderDown(True)
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """处理鼠标移动事件"""
        if self.isSliderDown():
            # 确保位置值为整数
            pos = int(event.position().x())
            # 计算新的值
            value = QStyle.sliderValueFromPosition(
                self.minimum(),
                self.maximum(),
                pos,
                self.width()
            )
            # 立即更新值
            self.setValue(value)
        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        """处理鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            # 确保位置值为整数
            pos = int(event.position().x())
            # 计算最终位置
            value = QStyle.sliderValueFromPosition(
                self.minimum(),
                self.maximum(),
                pos,
                self.width()
            )
            # 更新最终值
            self.setValue(value)
            # 释放滑块
            self.setSliderDown(False)
        super().mouseReleaseEvent(event)
        # 释放鼠标后将焦点返回到主窗口一次
        if self.window():
            self.window().setFocus()

    def wheelEvent(self, event):
        """处理鼠标滚轮事件"""
        if not self.isEnabled():
            return
            
        # 获取主窗口引用
        main_window = self.parent()
        while main_window and not isinstance(main_window, VideoPlayer):
            main_window = main_window.parent()
        
        if not main_window or not main_window.decoder:
            return
        
        # 获取当前帧位置
        current_pos = self.value()
        
        # 固定使用0.1秒的间隔
        frames_to_move = max(1, int(0.1 * main_window.fps))
        
        # 根据滚轮方向计算新的帧位置
        delta = event.angleDelta().y()
        if delta > 0:  # 向上滚动，后退
            new_pos = max(0, current_pos - frames_to_move)
        else:  # 向下滚动，前进
            new_pos = min(self.maximum(), current_pos + frames_to_move)
        
        # 更新滑块位置
        self.setValue(new_pos)
        
        # 使用OpenCV定位到指定帧
        if main_window.decoder.cap and main_window.decoder.cap.isOpened():
            main_window.decoder.cap.set(cv2.CAP_PROP_POS_FRAMES, new_pos)
            ret, frame = main_window.decoder.cap.read()
            if ret:
                # 更新缓存
                main_window.decoder.frame_buffer[new_pos] = frame
                # 限制缓存大小
                if len(main_window.decoder.frame_buffer) > main_window.decoder.buffer_size:
                    main_window.decoder.frame_buffer.popitem(last=False)
                main_window.decoder.current_pos = new_pos
                
                # 显示帧
                main_window.display_frame(frame)
                main_window.update_time_display(new_pos)
        
        event.accept()

    def keyPressEvent(self, event):
        """处理键盘事件"""
        if event.key() == Qt.Key.Key_Left:
            self.setValue(self.value() - self.singleStep())
        elif event.key() == Qt.Key.Key_Right:
            self.setValue(self.value() + self.singleStep())
        elif event.key() == Qt.Key.Key_PageUp:
            self.setValue(self.value() - self.pageStep())
        elif event.key() == Qt.Key.Key_PageDown:
            self.setValue(self.value() + self.pageStep())
        elif event.key() == Qt.Key.Key_Home:
            self.setValue(self.minimum())
        elif event.key() == Qt.Key.Key_End:
            self.setValue(self.maximum())
        else:
            super().keyPressEvent(event)

class VideoLoadWorker(QThread):
    progress = pyqtSignal(int)
    frame_loaded = pyqtSignal(object)
    finished = pyqtSignal()

    def __init__(self, video_path):
        super().__init__()
        self.video_path = video_path
        self.running = True

    def run(self):
        try:
            options = {
                "hwaccel": "auto",
                "threads": "4",
                "preset": "ultrafast",
                "tune": "fastdecode",
                "skip_loop_filter": "48"
            }

            container = av.open(self.video_path, options=options)
            stream = container.streams.video[0]
            stream.thread_type = "AUTO"

            # 预加载关键帧
            frames_to_load = 30
            loaded_frames = 0

            for frame in container.decode(stream):
                if not self.running:
                    break

                img = frame.to_ndarray(format="bgr24")
                self.frame_loaded.emit(img)
                loaded_frames += 1

                if loaded_frames >= frames_to_load:
                    break

                self.progress.emit(int(loaded_frames * 100 / frames_to_load))

            container.close()
            self.finished.emit()

        except Exception as e:
            print(f"视频加载错误: {str(e)}")
            self.finished.emit()

    def stop(self):
        self.running = False

class FrameBuffer:
    """优化的帧缓存管理器"""
    def __init__(self, max_size=60):
        # 根据视频帧率动态调整缓存大小
        self.buffer = {}
        self.max_size = max(max_size, 120)  # 至少保证1秒的高帧率缓存
        self.lock = Lock()
        
    def add(self, pos, frame):
        with self.lock:
            self.buffer[pos] = frame
            if len(self.buffer) > self.max_size:
                self.buffer.popitem(last=False)
                
    def get(self, pos):
        with self.lock:
            return self.buffer.get(pos)
            
    def clear(self):
        with self.lock:
            self.buffer.clear()

class FrameCache:
    def __init__(self, max_size=120):
        self.cache = OrderedDict()
        self.max_size = max_size
        self.lock = Lock()

    def get(self, pos):
        with self.lock:
            return self.cache.get(pos)

    def put(self, pos, frame):
        with self.lock:
            self.cache[pos] = frame
            if len(self.cache) > self.max_size:
                self.cache.popitem(last=False)

    def clear(self):
        with self.lock:
            self.cache.clear()

class VideoDecoder:
    def __init__(self, video_path):
        self.video_path = video_path
        self.container = None
        self.stream = None
        self.fps = 0
        self.total_frames = 0
        self.width = 0
        self.height = 0
        self.current_pos = 0
        self.frame_cache = FrameCache(max_size=120)
        self.thread_pool = ThreadPoolExecutor(max_workers=2)
        self.keyframes = []
        self.lock = Lock()

        try:
            # 配置FFmpeg选项，优化HEVC解码
            options = {
                "hwaccel": "auto",
                "threads": "4",
                "preset": "ultrafast",
                "tune": "fastdecode",
                "skip_loop_filter": "48",
                "skip_frame": "0",
                "strict": "experimental"
            }

            self.container = av.open(video_path, options=options)
            self.stream = self.container.streams.video[0]
            self.stream.thread_type = "AUTO"

            # 获取基本信息
            self.fps = float(self.stream.average_rate)
            self.total_frames = self.stream.frames
            self.width = self.stream.width
            self.height = self.stream.height

            if self.total_frames == 0:
                duration = float(self.container.duration) / av.time_base
                self.total_frames = int(duration * self.fps)

            # 初始化关键帧列表
            self._init_keyframes()

        except Exception as e:
            print(f"视频加载错误: {str(e)}")
            raise

    def _init_keyframes(self):
        """初始化关键帧列表"""
        try:
            container = av.open(self.video_path)
            stream = container.streams.video[0]
            self.keyframes = [p.pts for p in stream.packets if p.is_keyframe]
            container.close()
        except Exception as e:
            print(f"关键帧初始化错误: {str(e)}")

    def _find_nearest_keyframe(self, target_pts):
        """找到最近的关键帧"""
        if not self.keyframes:
            return 0
        return min(self.keyframes, key=lambda x: abs(x - target_pts))

    def seek_frame(self, frame_pos):
        """改进的帧定位方法"""
        try:
            with self.lock:
                # 检查缓存
                cached_frame = self.frame_cache.get(frame_pos)
                if cached_frame is not None:
                    return cached_frame

                # 计算目标时间戳
                target_pts = int(frame_pos * self.stream.time_base * self.fps)
                nearest_keyframe = self._find_nearest_keyframe(target_pts)

                # 重新打开容器以避免seek问题
                self.container.seek(nearest_keyframe, stream=self.stream)

                # 解码到目标帧
                for frame in self.container.decode(self.stream):
                    current_pts = frame.pts
                    if current_pts >= target_pts:
                        img = frame.to_ndarray(format="bgr24")
                        self.frame_cache.put(frame_pos, img)
                        return img

                return None

        except Exception as e:
            print(f"帧定位错误: {str(e)}")
            return None

    def preload_frames(self, current_pos, count=30):
        """异步预加载帧"""
        def _preload():
            try:
                for i in range(current_pos + 1, min(current_pos + count, self.total_frames)):
                    if self.frame_cache.get(i) is None:
                        self.seek_frame(i)
            except Exception as e:
                print(f"预加载错误: {str(e)}")

        self.thread_pool.submit(_preload)

    """改进的视频解码器，统一使用 PyAV 和 FFmpeg"""
    def __init__(self, video_path):
        self.video_path = video_path
        self.container = None
        self.stream = None
        self.cap = None  # 保留 OpenCV 作为备选
        self.fps = 0
        self.total_frames = 0
        self.width = 0
        self.height = 0
        self.current_pos = 0
        self.frame_buffer = OrderedDict()
        self.buffer_size = 30  # 缓存大小
        self.lock = Lock()
        self.decoder_type = "unknown"  # 添加解码器类型标识
        
        try:
            # 首先尝试使用 PyAV 打开
            self.container = av.open(video_path)
            self.stream = self.container.streams.video[0]
            
            # 获取基本信息
            self.fps = float(self.stream.average_rate)
            self.total_frames = self.stream.frames
            self.width = self.stream.width
            self.height = self.stream.height
            
            # 如果 frames 为 0，尝试计算总帧数
            if self.total_frames == 0:
                duration = float(self.container.duration) / av.time_base
                self.total_frames = int(duration * self.fps)
                
            # 同时也打开 OpenCV 作为备选
            self.cap = cv2.VideoCapture(video_path)
            
            # 设置解码器类型
            self.decoder_type = "PyAV"
            
        except Exception as e:
            print(f"PyAV 初始化失败: {str(e)}")
            # 如果 PyAV 失败，回退到 OpenCV
            self.cap = cv2.VideoCapture(video_path)
            if not self.cap.isOpened():
                raise Exception(f"无法打开视频文件: {str(e)}")
                
            self.fps = self.cap.get(cv2.CAP_PROP_FPS)
            self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            self.height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            # 设置解码器类型
            self.decoder_type = "OpenCV"
    
    def __del__(self):
        """清理资源"""
        if self.container:
            self.container.close()
        if self.cap:
            self.cap.release()
    
    def seek_frame(self, frame_pos):
        """改进的帧定位方法"""
        with self.lock:
            try:
                # 检查缓存
                if frame_pos in self.frame_buffer:
                    self.current_pos = frame_pos
                    return self.frame_buffer[frame_pos]
                
                # 首先尝试使用 PyAV
                if self.container and self.stream:
                    try:
                        # 计算时间戳
                        time_base = self.stream.time_base
                        timestamp = int(frame_pos / self.fps / time_base)
                        
                        # 定位到指定位置
                        self.container.seek(timestamp, stream=self.stream)
                        
                        # 读取帧
                        for frame in self.container.decode(video=0):
                            img = frame.to_ndarray(format='bgr24')
                            # 更新缓存
                            self.frame_buffer[frame_pos] = img
                            # 限制缓存大小
                            if len(self.frame_buffer) > self.buffer_size:
                                self.frame_buffer.popitem(last=False)
                            self.current_pos = frame_pos
                            return img
                            
                    except Exception as av_error:
                        print(f"PyAV 读取失败: {str(av_error)}")
                
                # 如果 PyAV 失败，回退到 OpenCV
                if self.cap and self.cap.isOpened():
                    self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_pos)
                    ret, frame = self.cap.read()
                    if ret:
                        # 更新缓存
                        self.frame_buffer[frame_pos] = frame
                        # 限制缓存大小
                        if len(self.frame_buffer) > self.buffer_size:
                            self.frame_buffer.popitem(last=False)
                        self.current_pos = frame_pos
                        return frame
                
                # 如果所有方法都失败
                return None
    
            except Exception as e:
                print(f"帧定位失败: {str(e)}")
                return None
    
    def read_next_frame(self):
        """读取下一帧"""
        return self.seek_frame(self.current_pos + 1)
    
    def read_prev_frame(self):
        """读取上一帧"""
        if self.current_pos > 0:
            return self.seek_frame(self.current_pos - 1)
        return None
        
    def seek_frame_precise(self, frame_pos, exact_seconds=None):
        """简化的精确帧定位方法，只使用OpenCV定位
        
        Args:
            frame_pos: 目标帧索引
            exact_seconds: 可选的精确时间戳（秒），不使用
        """
        with self.lock:
            try:
                # 确保使用整数帧索引
                frame_pos = int(frame_pos)
                
                # 检查缓存
                if frame_pos in self.frame_buffer:
                    self.current_pos = frame_pos
                    return self.frame_buffer[frame_pos]
                
                # 直接使用OpenCV进行帧定位
                if self.cap and self.cap.isOpened():
                    # 使用帧索引定位
                    self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_pos)
                    ret, frame = self.cap.read()
                    
                    if ret:
                        # 更新缓存
                        self.frame_buffer[frame_pos] = frame
                        # 限制缓存大小
                        if len(self.frame_buffer) > self.buffer_size:
                            self.frame_buffer.popitem(last=False)
                        self.current_pos = frame_pos
                        
                        # 获取当前帧的时间戳（毫秒）
                        frame_time_ms = self.cap.get(cv2.CAP_PROP_POS_MSEC)
                        print(f"OpenCV帧索引定位成功: 帧={frame_pos}, 时间={frame_time_ms}ms")
                        return frame
                
                # 如果定位失败
                print(f"帧定位失败: 帧={frame_pos}")
                return None
            
            except Exception as e:
                print(f"精确帧定位失败: {str(e)}")
                return None

    def get_fps(self):
        return self.fps

class VideoPlayer(QMainWindow):
    def get_video_identifier(self, video_path):
        """获取视频文件的唯一标识符
        
        Args:
            video_path (str): 视频文件路径
            
        Returns:
            str: 视频文件的唯一标识符（基于文件大小和修改时间）
        """
        try:
            if not video_path or not os.path.exists(video_path):
                return None
                
            # 获取文件信息
            file_stat = os.stat(video_path)
            # 使用文件大小和最后修改时间作为标识符
            identifier = f"{os.path.basename(video_path)}_{file_stat.st_size}_{file_stat.st_mtime}"
            return identifier
            
        except Exception as e:
            print(f"获取视频标识符时出错: {str(e)}")
            return None
            
    def __init__(self):
        """初始化视频播放器"""
        super().__init__()
        self.setWindowTitle("视频帧提取工具")
        self.setAcceptDrops(True)  # 允许拖放文件
        
        # 设置文件路径
        app_data_dir = os.path.join(os.getenv('APPDATA'), 'VideoCapture')
        if not os.path.exists(app_data_dir):
            os.makedirs(app_data_dir)
        self.settings_file = os.path.join(app_data_dir, 'settings.json')
        self.replaced_frames_dir = os.path.join(app_data_dir, 'replaced_frames')  # 添加替换帧记录目录
        if not os.path.exists(self.replaced_frames_dir):
            os.makedirs(self.replaced_frames_dir)
        
        # 初始化变量
        self.video_path = ""
        self.output_path = ""
        self.decoder = None
        self.frame_buffer = FrameBuffer()
        self.current_frame = None
        self.timer = QTimer()
        self.timer.timeout.connect(self.continuous_seek)
        self.seek_direction = 1  # 1表示前进，-1表示后退
        self.seeking = False
        self.save_format = "jpg"
        self.total_frames = 0
        self.frame_positions = []
        self.is_dragging = False
        self.always_on_top = False
        self.keyframes = []
        self.last_preload_pos = -1
        self.smooth_seek_timer = None
        self.batch_manager = None
        self.stitch_worker = None
        self.video_load_worker = None
        self.keyframe_worker = None
        self.smooth_mode = True
        self.preview_dialog = None
        self.stitch_mode = "grid"  # 默认为网格模式
        self.fps = 0
        self.last_preview_time = 0
        self.preview_update_timer = QTimer()
        self.preview_update_timer.setSingleShot(True)
        self.preview_update_timer.timeout.connect(self.update_preview_now)
        self.replace_frames = {}  # 用于存储要替换的帧
        
        # 定义其他变量
        self.cap = None
        self.current_frame = None
        self.decoder = None
        self.seek_timer = QTimer()
        self.seek_timer.timeout.connect(self.continuous_seek)
        self.seek_direction = 0
        self.animation_worker = None
        self.is_dragging = False
        self.last_frame_pos = 0
        self.frame_buffer = {}
        self.buffer_size = 30  # 增加缓存大小
        self.keyframe_positions = []  # 存储关键帧位置
        self.last_frame_time = 0
        self.frame_update_interval = 50  # 毫秒
        
        # 添加新的定时器用于平滑更新
        self.smooth_timer = QTimer()
        self.smooth_timer.setInterval(16)  # ~60fps
        self.smooth_timer.timeout.connect(self.smooth_update)
        
        # 添加拖动状态
        self.is_seeking = False
        self.seek_target = 0
        
        self.load_worker = None
        
        # 默认设置
        self.default_settings = {
            'save_path': os.path.expanduser("~/Desktop"),
            'size_mode': "原始尺寸",
            'width': 480,
            'height': 270,
            'seek_time': 1,
            'duration': 3,
            'fps': 24,
            'loop_mode': "无限循环",
            'loop_count': 1,
            'quality_mode': "平衡",
            'stitch_mode': "单独保存",
            'save_to_video_dir': False  # 添加默认值
        }
        
        # 先初始化UI
        self.init_ui()
        
        # 然后加载设置（只在此处加载一次）
        self.load_settings()
        
        # 加载并应用质量设置
        self.load_quality_setting()
        
        # 添加窗口置顶快捷键
        self.toggle_top_shortcut = QShortcut(QKeySequence("Ctrl+T"), self)
        self.toggle_top_shortcut.activated.connect(self.toggle_window_top)
        
        # 添加窗口置顶状态
        self.is_top = False
        
        self.keyframe_worker = None
        self.last_seek_time = 0
        self.seek_interval = 50  # 毫秒
        
        # 添加预览设置对话框实例
        self.preview_dialog = None
        
        # 在初始化方法中添加这一行
        self.preview_frame_positions = []
        
        # 添加一个字典来存储替换的帧
        self.replaced_frames = {}  # 格式: {原始帧索引: 替换帧数据}
        
    def init_ui(self):
        # 创建主窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 设置更合适的窗口大小
        self.setMinimumSize(1200, 720)  # 增加默认窗口大小
        
        # 创建主布局
        main_layout = QHBoxLayout()
        main_layout.setSpacing(10)  # 设置布局间距
        
        # 左侧布局 - 视频预览
        left_layout = QVBoxLayout()
        left_layout.setSpacing(5)  # 减小控件间距
        
        # 修改视频容器设置
        video_container = QWidget()
        video_container.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Preferred)
        video_container_layout = QVBoxLayout(video_container)
        video_container_layout.setContentsMargins(10, 10, 10, 10)  # 添加内边距
        video_container_layout.setSpacing(0)
        
        # 设置视频容器的样式
        video_container.setStyleSheet("""
            QWidget {
                background-color: #2c3e50;
                border-radius: 12px;
            }
        """)
        
        # 创建一个用于显示阴影效果的容器
        shadow_effect = QGraphicsDropShadowEffect()
        shadow_effect.setBlurRadius(20)
        shadow_effect.setColor(QColor(0, 0, 0, 80))
        shadow_effect.setOffset(0, 0)
        video_container.setGraphicsEffect(shadow_effect)
        
        # 修改视频显示标签设置
        self.image_label = QLabel()
        self.image_label.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Preferred)
        self.image_label.setMinimumSize(320, 240)  # 设置最小尺寸
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setStyleSheet("""
            QLabel {
                background-color: #1e2a38;
                color: #ecf0f1;
                font-size: 16px;
                border-radius: 8px;
                padding: 20px;
            }
        """)
        self.image_label.setText("将视频文件拖放到这里")
        self.image_label.wheelEvent = self.video_area_wheel_event  # 添加鼠标滚轮事件处理
        video_container_layout.addWidget(self.image_label)
        
        # 将视频容器添加到左侧布局，并设置拉伸因子
        left_layout.addWidget(video_container, stretch=8)  # 增加视频区域的比例
        
        # 添加控制组件
        control_group = QVBoxLayout()
        control_group.setSpacing(2)  # 减小控件间距
        
        # 进度条
        self.slider = ClickableSlider(Qt.Orientation.Horizontal)
        self.slider.setEnabled(False)
        self.slider.valueChanged.connect(self.slider_changed)
        self.slider.setStyleSheet("""
            QSlider {
                height: 24px;
                background: transparent;
            }
            
            QSlider::groove:horizontal {
                height: 3px;
                background: #3d4c5e;
                border-radius: 1px;
                margin: 0px;
            }
            
            QSlider::sub-page:horizontal {
                height: 3px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, 
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 1px;
            }
            
            QSlider::handle:horizontal {
                width: 16px;
                height: 16px;
                margin: -7px 0;
                background: qradialgradient(cx:0.5, cy:0.5, radius:0.5, fx:0.5, fy:0.5,
                    stop:0 #ffffff,
                    stop:0.85 #ffffff,
                    stop:0.901 #3498db,
                    stop:1 #3498db);
                border-radius: 8px;
            }
            
            QSlider::handle:horizontal:hover {
                width: 18px;
                height: 18px;
                margin: -8px -1px;
                background: qradialgradient(cx:0.5, cy:0.5, radius:0.5, fx:0.5, fy:0.5,
                    stop:0 #ffffff,
                    stop:0.85 #ffffff,
                    stop:0.901 #2980b9,
                    stop:1 #2980b9);
                border-radius: 9px;
            }
            
            QSlider::handle:horizontal:pressed {
                background: qradialgradient(cx:0.5, cy:0.5, radius:0.5, fx:0.5, fy:0.5,
                    stop:0 #f0f0f0,
                    stop:0.85 #f0f0f0,
                    stop:0.901 #2472a4,
                    stop:1 #2472a4);
            }
            
            QSlider:disabled {
                opacity: 0.6;
            }
            
            QSlider::handle:disabled {
                background: qradialgradient(cx:0.5, cy:0.5, radius:0.5, fx:0.5, fy:0.5,
                    stop:0 #ecf0f1,
                    stop:0.85 #ecf0f1,
                    stop:0.901 #bdc3c7,
                    stop:1 #bdc3c7);
            }
        """)
        control_group.addWidget(self.slider)
        
        # 时间和控制布局（包含截图按钮）
        time_control_layout = QVBoxLayout()  # 垂直布局，包含两行控件
        time_control_layout.setSpacing(10)  # 增加行间距
        
        # 第一行：时间显示和跳转控制
        first_row_layout = QHBoxLayout()
        first_row_layout.setSpacing(8)  # 减小间距
        
        # 创建时间显示组
        time_display_group = QHBoxLayout()
        time_display_group.setSpacing(4)  # 减小间距
        
        # 时间显示标签
        self.time_label = QLabel("00:00:00:00")
        self.time_label.setFixedWidth(85)
        self.time_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-weight: bold;
                font-size: 13px;
                padding: 2px 4px;
                background: #34495e;
                border-radius: 3px;
            }
        """)
        time_display_group.addWidget(self.time_label)
        
        # 添加 / 分隔符
        separator_label = QLabel("/")
        separator_label.setStyleSheet("color: #95a5a6; font-size: 12px;")
        time_display_group.addWidget(separator_label)
        
        # 添加总时间标签
        self.total_time_label = QLabel("00:00:00:00")
        self.total_time_label.setFixedWidth(85)
        self.total_time_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-weight: bold;
                font-size: 13px;
                padding: 2px 4px;
                background: #34495e;
                border-radius: 3px;
            }
        """)
        time_display_group.addWidget(self.total_time_label)
        first_row_layout.addLayout(time_display_group)
        
        # 添加一个较小的弹性空间
        first_row_layout.addStretch(1)  # 改为整数1
        
        # 跳转控制组 - 使用扁平化设计
        jump_container = QWidget()
        jump_container.setStyleSheet("""
            QWidget {
                background: #34495e;
                border-radius: 3px;
                padding: 2px;
            }
        """)
        jump_layout = QHBoxLayout(jump_container)
        jump_layout.setSpacing(2)
        jump_layout.setContentsMargins(4, 2, 4, 2)
        
        # 时间输入框样式
        time_input_style = """
            QSpinBox {
                padding: 1px 2px;
                border: none;
                border-radius: 2px;
                background: #2c3e50;
                color: #ecf0f1;
                min-width: 30px;
                max-width: 30px;
                font-size: 12px;
            }
            QSpinBox:hover {
                background: #243342;
            }
            QSpinBox:focus {
                background: #1a252f;
            }
        """
        
        # 时间输入框
        self.hour_input = QSpinBox()
        self.minute_input = QSpinBox()
        self.second_input = QSpinBox()
        self.frame_input = QSpinBox()
        
        # 设置时间输入框的范围和样式
        for spinbox, maximum in [(self.hour_input, 23), (self.minute_input, 59), 
                               (self.second_input, 59), (self.frame_input, 99)]:
            spinbox.setRange(0, maximum)
            spinbox.setButtonSymbols(QSpinBox.ButtonSymbols.NoButtons)
            spinbox.setAlignment(Qt.AlignmentFlag.AlignCenter)
            spinbox.setStyleSheet(time_input_style)
        
        # 分隔符样式
        separator_style = "QLabel { color: #95a5a6; font-size: 12px; padding: 0 1px; }"
        
        # 添加时间输入框和分隔符
        jump_layout.addWidget(self.hour_input)
        separator1 = QLabel(":")
        separator1.setStyleSheet(separator_style)
        jump_layout.addWidget(separator1)
        jump_layout.addWidget(self.minute_input)
        separator2 = QLabel(":")
        separator2.setStyleSheet(separator_style)
        jump_layout.addWidget(separator2)
        jump_layout.addWidget(self.second_input)
        separator3 = QLabel(".")
        separator3.setStyleSheet(separator_style)
        jump_layout.addWidget(separator3)
        jump_layout.addWidget(self.frame_input)
        
        # 跳转按钮
        self.goto_btn = QPushButton("跳转")
        self.goto_btn.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                border-radius: 2px;
                padding: 3px 8px;
                font-size: 12px;
                font-weight: bold;
                margin-left: 2px;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
            QPushButton:pressed {
                background-color: #219a52;
            }
        """)
        self.goto_btn.clicked.connect(self.goto_time)
        jump_layout.addWidget(self.goto_btn)
        
        first_row_layout.addWidget(jump_container)
        
        # 将第一行添加到主布局
        time_control_layout.addLayout(first_row_layout)
        
        # 第二行：快进退和截图按钮
        second_row_layout = QHBoxLayout()
        second_row_layout.setSpacing(8)  # 减小控件间距
        
        # 快进退设置 - 使用扁平化容器
        seek_container = QWidget()
        seek_container.setStyleSheet("""
            QWidget {
                background: #34495e;
                border-radius: 3px;
                padding: 2px;
            }
        """)
        seek_layout = QHBoxLayout(seek_container)
        seek_layout.setSpacing(4)
        seek_layout.setContentsMargins(4, 2, 4, 2)
        
        # 快进退标签
        seek_label = QLabel("速度")
        seek_label.setStyleSheet("""
            QLabel {
                color: #ecf0f1;
                font-size: 12px;
                padding: 0 2px;
            }
        """)
        seek_layout.addWidget(seek_label)
        
        # 快进退输入框
        self.seek_input = QDoubleSpinBox()
        self.seek_input.setRange(0.1, 60.0)
        self.seek_input.setValue(0.1)  # 默认值为0.1秒
        self.seek_input.setDecimals(1)
        self.seek_input.setSingleStep(0.1)
        self.seek_input.setSuffix("秒")
        self.seek_input.setStyleSheet("""
            QDoubleSpinBox {
                padding: 1px 2px;
                border: none;
                border-radius: 2px;
                background: #2c3e50;
                color: #ecf0f1;
                min-width: 60px;
                max-width: 60px;
                font-size: 12px;
            }
            QDoubleSpinBox:hover {
                background: #243342;
            }
            QDoubleSpinBox:focus {
                background: #1a252f;
            }
            QDoubleSpinBox::up-button, QDoubleSpinBox::down-button {
                width: 0;
                border: none;
            }
        """)
        seek_layout.addWidget(self.seek_input)
        
        # 添加快进退按钮
        self.prev_btn = QPushButton("◀")
        self.next_btn = QPushButton("▶")
        self.prev_btn.setEnabled(False)
        self.next_btn.setEnabled(False)
        self.prev_btn.pressed.connect(self.start_backward)
        self.prev_btn.released.connect(self.stop_seeking)
        self.next_btn.pressed.connect(self.start_forward)
        self.next_btn.released.connect(self.stop_seeking)
        
        # 设置快进退按钮样式
        button_style = """
            QPushButton {
                min-width: 24px;
                max-width: 24px;
                min-height: 24px;
                max-height: 24px;
                padding: 0px;
                border-radius: 2px;
                background-color: #2c3e50;
                color: #ecf0f1;
                border: none;
                font-size: 14px;
                font-weight: bold;
                margin: 0 2px;
            }
            QPushButton:hover {
                background-color: #243342;
            }
            QPushButton:pressed {
                background-color: #1a252f;
            }
            QPushButton:disabled {
                background-color: #34495e;
                color: #95a5a6;
            }
        """
        self.prev_btn.setStyleSheet(button_style)
        self.next_btn.setStyleSheet(button_style)
        
        # 添加快进退按钮到布局
        seek_layout.addWidget(self.prev_btn)
        seek_layout.addWidget(self.next_btn)
        
        second_row_layout.addWidget(seek_container)
        second_row_layout.addStretch(1)  # 添加弹性空间
        
        # 截图按钮 - 使用现代风格
        self.capture_btn = QPushButton("截图")  # 移除图标
        self.capture_btn.setFixedWidth(140)  # 适中的宽度
        self.capture_btn.setFixedHeight(42)  # 适中的高度
        self.capture_btn.setEnabled(False)
        self.capture_btn.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                border-radius: 21px;
                font-size: 16px;
                font-weight: bold;
                padding: 6px 12px;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
            QPushButton:pressed {
                background-color: #219a52;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
                color: #ecf0f1;
            }
        """)
        self.capture_btn.clicked.connect(self.capture_frame)  # 直接连接信号
        second_row_layout.addWidget(self.capture_btn)
        
        # 将第二行添加到主布局
        time_control_layout.addLayout(second_row_layout)
        
        # 添加时间控制布局到控制组
        control_group.addLayout(time_control_layout)
        
        left_layout.addLayout(control_group, stretch=2)  # 增加控制区域的比例
        left_layout.addStretch(1)  # 添加弹性空间到底部
        
        # 中间布局 - 预览图
        middle_layout = QVBoxLayout()
        middle_layout.setSpacing(10)
        middle_layout.setContentsMargins(5, 5, 5, 5)
        
        # 预览图按钮布局
        preview_btn_layout = QHBoxLayout()
        preview_btn_layout.setSpacing(10)
        
        # 预览图设置按钮
        preview_settings_btn = QPushButton("预览设置")
        preview_settings_btn.clicked.connect(self.show_preview_settings)
        preview_settings_btn.setFixedHeight(30)
        preview_settings_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1c6ea4;
            }
        """)
        preview_btn_layout.addWidget(preview_settings_btn)
        
        # 添加保存预览图按钮
        save_preview_btn = QPushButton("保存预览图")
        save_preview_btn.clicked.connect(self.save_preview)
        save_preview_btn.setEnabled(False)
        save_preview_btn.setFixedHeight(30)
        save_preview_btn.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
            QPushButton:pressed {
                background-color: #219a52;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        self.save_preview_btn = save_preview_btn
        preview_btn_layout.addWidget(save_preview_btn)
        
        # 添加恢复默认帧按钮
        restore_frames_btn = QPushButton("恢复默认帧")
        restore_frames_btn.clicked.connect(self.restore_default_frames)
        restore_frames_btn.setEnabled(False)
        restore_frames_btn.setFixedHeight(30)
        restore_frames_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        self.restore_frames_btn = restore_frames_btn
        preview_btn_layout.addWidget(restore_frames_btn)
        
        # 添加弹性空间
        preview_btn_layout.addStretch(1)
        
        middle_layout.addLayout(preview_btn_layout)
        
        # 创建预览图容器
        self.preview_area = QLabel()
        self.preview_area.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.preview_area.setText("点击预览设置生成预览图")
        # 将尺寸策略从Expanding改为Fixed
        self.preview_area.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        # 设置固定宽度和高度
        self.preview_area.setFixedWidth(360)
        self.preview_area.setFixedHeight(660)
        self.preview_area.setMinimumHeight(300)  # 增加最小高度
        self.preview_area.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                              stop:0 #ffffff, 
                              stop:1 #f8f9fa);
                color: #333333;
                border: 1px solid #e6e6e6;
                border-radius: 10px;
                padding: 12px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)  # 增加阴影模糊度
        shadow.setColor(QColor(0, 0, 0, 25))  # 更淡的阴影
        shadow.setOffset(0, 3)  # 适当增加向下偏移
        self.preview_area.setGraphicsEffect(shadow)
        
        middle_layout.addWidget(self.preview_area, 1)
        
        # 添加预览信息标签
        self.preview_info = QLabel("")
        self.preview_info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.preview_info.setFixedHeight(0)  # 将高度设为0，使其不可见
        self.preview_info.setStyleSheet("""
            QLabel {
                background-color: transparent;
                color: #3498db;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                font-size: 12px;
                margin-top: 0;
                padding: 0;
                border: none;
            }
        """)
        # middle_layout.addWidget(self.preview_info)  # 注释掉，不添加到布局中
        
        main_layout.addLayout(middle_layout, stretch=3)
        
        # 右侧布局 - 控制面板
        right_layout = QVBoxLayout()
        right_layout.setSpacing(10)
        
        # 输出设置组
        output_group = QGroupBox("输出设置")
        output_layout = QVBoxLayout()
        output_layout.setSpacing(5)
        
        # 尺寸设置
        size_layout = QGridLayout()
        size_layout.setSpacing(5)
        size_layout.addWidget(QLabel("输出尺寸:"), 0, 0)
        self.size_combo = QComboBox()
        self.size_combo.addItems(["原始尺寸", "自定义尺寸"])
        self.size_combo.currentTextChanged.connect(self.on_size_change)
        size_layout.addWidget(self.size_combo, 0, 1, 1, 3)
        
        size_layout.addWidget(QLabel("宽:"), 1, 0)
        self.width_input = QSpinBox()
        self.width_input.setRange(0, 9999)
        self.width_input.setValue(480)
        self.width_input.setEnabled(False)
        self.width_input.valueChanged.connect(self.on_width_changed)
        size_layout.addWidget(self.width_input, 1, 1)
        
        size_layout.addWidget(QLabel("高:"), 1, 2)
        self.height_input = QSpinBox()
        self.height_input.setRange(0, 9999)
        self.height_input.setValue(270)
        self.height_input.setEnabled(False)
        self.height_input.valueChanged.connect(self.on_height_changed)
        size_layout.addWidget(self.height_input, 1, 3)
        
        output_layout.addLayout(size_layout)
        
        # 保存路径设置
        path_layout = QHBoxLayout()
        path_layout.addWidget(QLabel("保存路径:"))
        self.path_input = QLineEdit()
        self.path_input.setText(os.path.expanduser("~/Desktop"))
        path_layout.addWidget(self.path_input)
        self.browse_btn = QPushButton("浏览")
        self.browse_btn.clicked.connect(self.browse_path)
        path_layout.addWidget(self.browse_btn)
        
        # 添加保存到视频目录的复选框
        self.save_to_video_dir_checkbox = QCheckBox("保存到视频目录")
        self.save_to_video_dir_checkbox.setToolTip("选中后，将自动保存到当前打开视频的目录下")
        self.save_to_video_dir_checkbox.stateChanged.connect(self.update_save_path)
        path_layout.addWidget(self.save_to_video_dir_checkbox)
        
        output_layout.addLayout(path_layout)
        
        # 在输出设置组中添加格式选择（在 output_layout 中添加）
        # 在 size_layout 之后，path_layout 之前添加
        format_layout = QHBoxLayout()
        format_layout.addWidget(QLabel("保存格式:"))
        self.save_format_combo = QComboBox()
        self.save_format_combo.addItems(["PNG", "JPG"])
        self.save_format_combo.setCurrentText("PNG")  # 默认使用 PNG
        format_layout.addWidget(self.save_format_combo)
        
        # JPG 质量设置
        self.jpg_quality_spin = QSpinBox()
        self.jpg_quality_spin.setRange(1, 100)
        self.jpg_quality_spin.setValue(95)  # 默认质量95
        self.jpg_quality_spin.setSuffix("%")
        self.jpg_quality_spin.setVisible(False)  # 初始隐藏
        format_layout.addWidget(QLabel("质量:"))
        format_layout.addWidget(self.jpg_quality_spin)
        
        # 添加到输出布局
        output_layout.addLayout(format_layout)
        
        # 连接信号
        self.save_format_combo.currentTextChanged.connect(self.on_save_format_changed)
        self.save_format_combo.currentTextChanged.connect(self.on_setting_changed)
        self.jpg_quality_spin.valueChanged.connect(self.on_setting_changed)
        
        output_group.setLayout(output_layout)
        right_layout.addWidget(output_group)
        
        # 动画设置组
        animation_group = QGroupBox("动画设置")
        animation_layout = QVBoxLayout()
        animation_layout.setSpacing(5)
        
        # 动画参数设置
        params_grid = QGridLayout()
        params_grid.setSpacing(5)
        
        params_grid.addWidget(QLabel("格式:"), 0, 0)
        format_label = QLabel("WebP")  # 创建一个显示 WebP 的标签
        format_label.setStyleSheet("color: black;")  # 将颜色从 #666 改为 black
        
        params_grid.addWidget(QLabel("时长:"), 0, 2)
        self.duration_input = QSpinBox()
        self.duration_input.setRange(1, 60)
        self.duration_input.setValue(3)
        self.duration_input.setSuffix("秒")
        params_grid.addWidget(self.duration_input, 0, 3)
        
        # 在 init_ui 方法中修改帧率输入框的设置
        params_grid.addWidget(QLabel("帧率:"), 1, 0)
        fps_layout = QVBoxLayout()
        self.fps_input = QDoubleSpinBox()  # 改用 QDoubleSpinBox
        self.fps_input.setRange(1.0, 120.0)  # 扩大范围支持更多帧率
        self.fps_input.setDecimals(3)  # 支持3位小数
        self.fps_input.setValue(24.0)  # 默认值
        self.fps_input.setSuffix(" fps")  # 添加单位显示
        fps_layout.addWidget(self.fps_input)

        # 修改原帧率标签样式和位置
        self.fps_label = QLabel()  # 原帧率显示标签
        self.fps_label.setStyleSheet("color: black;")  # 设置颜色为黑色
        fps_layout.addWidget(self.fps_label)

        params_grid.addLayout(fps_layout, 1, 1)  # 将垂直布局添加到网格布局
        
        animation_layout.addLayout(params_grid)
        
        # 质量设置
        quality_layout = QHBoxLayout()
        quality_layout.addWidget(QLabel("质量模式:"))
        self.quality_combo = QComboBox()
        self.quality_combo.addItems(["快速", "平衡", "高质量", "完全无损"])
        quality_layout.addWidget(self.quality_combo)
        animation_layout.addLayout(quality_layout)
        
        # 在 animation_layout 中添加拼接设置
        # 在 quality_layout 之后添加：

        # 拼接设置
        stitch_layout = QHBoxLayout()
        stitch_layout.addWidget(QLabel("拼接模式:"))
        self.stitch_combo = QComboBox()
        self.stitch_combo.addItems(["单独保存", "横向拼接"])
        self.stitch_combo.currentTextChanged.connect(self.on_stitch_mode_changed)
        stitch_layout.addWidget(self.stitch_combo)
        animation_layout.addLayout(stitch_layout)
        
        animation_group.setLayout(animation_layout)
        right_layout.addWidget(animation_group)
        
        # 在动画位置组之前添加状态标签
        position_group = QGroupBox("动画位置")
        position_layout = QVBoxLayout()
        
        # 添加状态标签到动画位置组的上方
        self.status_label = QLabel()
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 5px;
                font-size: 12px;
                color: #2e7d32;
                background-color: transparent;
            }
        """)
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.status_label.setWordWrap(True)  # 允许文字换行
        self.status_label.setMinimumHeight(20)  # 设置最小高度
        self.status_label.setMaximumHeight(40)  # 设置最大高度，防止过度扩展
        self.status_label.setFixedWidth(300)  # 设置固定宽度
        right_layout.addWidget(self.status_label)  # 添加到右侧布局
        
        # 然后是动画位置组的其他内容
        self.position_list = DraggableListWidget()
        self.position_list.set_main_window(self)  # 设置主窗口引用
        self.position_list.setMaximumHeight(100)  # 减小列表高度
        position_layout.addWidget(self.position_list)
        
        position_btn_layout = QHBoxLayout()
        self.add_position_btn = QPushButton("添加位置")
        self.remove_position_btn = QPushButton("删除位置")
        self.clear_positions_btn = QPushButton("清空位置")
        
        position_btn_layout = QHBoxLayout()
        self.add_position_btn = QPushButton("添加位置")
        self.remove_position_btn = QPushButton("删除位置")
        self.clear_positions_btn = QPushButton("清空位置")
        
        position_btn_layout.addWidget(self.add_position_btn)
        position_btn_layout.addWidget(self.remove_position_btn)
        position_btn_layout.addWidget(self.clear_positions_btn)
        position_layout.addLayout(position_btn_layout)
        
        position_group.setLayout(position_layout)
        right_layout.addWidget(position_group)
        
        # 操作按钮和进度条
        bottom_layout = QVBoxLayout()
        bottom_layout.setSpacing(5)
        
        # 操作按钮
        action_layout = QHBoxLayout()
        self.make_gif_btn = QPushButton("制作动画")
        self.make_gif_btn.setEnabled(False)
        self.clear_btn = QPushButton("清空视频")
        self.clear_btn.setEnabled(False)
        
        # 在操作按钮布局中添加取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setEnabled(False)
        self.cancel_btn.clicked.connect(self.cancel_animation)
        action_layout.addWidget(self.make_gif_btn)
        action_layout.addWidget(self.clear_btn)
        action_layout.addWidget(self.cancel_btn)
        
        bottom_layout.addLayout(action_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        bottom_layout.addWidget(self.progress_bar)
        
        right_layout.addLayout(bottom_layout)
        
        # 添加左右布局到主布局
        main_layout.addLayout(left_layout, stretch=4)  # 增加左侧布局的比例
        main_layout.addLayout(right_layout, stretch=2)
        
        central_widget.setLayout(main_layout)
        
        # 连接按钮信号
        self.capture_btn.clicked.disconnect()  # 先断开所有连接
        self.capture_btn.clicked.connect(self.capture_frame)  # 重新连接
        self.make_gif_btn.clicked.connect(self.create_animation)
        self.clear_btn.clicked.connect(self.clear_video)
        self.add_position_btn.clicked.connect(self.add_current_position)
        self.remove_position_btn.clicked.connect(self.remove_selected_position)
        self.clear_positions_btn.clicked.connect(self.clear_positions)
        
        # 加载上次选择的质量模式
        self.load_quality_setting()
        
        # 添加设置改变的信号连接
        self.quality_combo.currentTextChanged.connect(self.on_setting_changed)
        self.duration_input.valueChanged.connect(self.on_setting_changed)
        self.fps_input.valueChanged.connect(self.on_setting_changed)
        self.size_combo.currentTextChanged.connect(self.on_setting_changed)
        self.width_input.valueChanged.connect(self.on_setting_changed)
        self.height_input.valueChanged.connect(self.on_setting_changed)
        self.path_input.textChanged.connect(self.on_setting_changed)
        
        # 在 init_ui 方法中，在右侧布局的最开始添加置顶按钮
        # 在 right_layout = QVBoxLayout() 之后添加：

        # 添加窗口置顶按钮
        top_btn_layout = QHBoxLayout()
        self.top_window_btn = QPushButton("窗口置顶")
        self.top_window_btn.setCheckable(True)  # 使按钮可切换
        self.top_window_btn.setStyleSheet("""
            QPushButton {
                padding: 5px 10px;
                border-radius: 3px;
                background-color: #f0f0f0;
            }
            QPushButton:checked {
                background-color: #2196F3;
                color: white;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:checked:hover {
                background-color: #1976D2;
            }
        """)
        self.top_window_btn.clicked.connect(self.toggle_window_top)
        top_btn_layout.addWidget(self.top_window_btn)
        top_btn_layout.addStretch()

        # 将布局添加到右侧布局
        right_layout.addLayout(top_btn_layout)
        
        # 连接预览图相关的信号
        self.connect_preview_signals()
        
        # 在预览按钮布局中添加保存所有单帧按钮
        save_all_frames_btn = QPushButton("保存所有单帧")
        save_all_frames_btn.clicked.connect(self.save_all_preview_frames)
        save_all_frames_btn.setFixedHeight(30)
        save_all_frames_btn.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
            QPushButton:pressed {
                background-color: #a04000;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        self.save_all_frames_btn = save_all_frames_btn
        save_all_frames_btn.setEnabled(False)  # 初始禁用，直到生成预览图
        preview_btn_layout.addWidget(save_all_frames_btn)
        
        # 在创建save_format_combo的地方，添加连接代码
        # 在创建save_format_combo后添加：
        self.save_format_combo.currentTextChanged.connect(self.update_format_ui)
        
        # 在init_ui方法的末尾调用一次update_format_ui
        # 在init_ui方法的末尾添加：
        self.update_format_ui()
        
    def dragEnterEvent(self, event):
        if event.mimeData().hasUrls():
            event.acceptProposedAction()

    def dropEvent(self, event):
        """优化的处理释放事件，增强解码器信息显示"""
        urls = event.mimeData().urls()
        if urls:
            file_path = urls[0].toLocalFile()
            
            # 检查文件扩展名
            file_ext = os.path.splitext(file_path)[1].lower()
            valid_video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm']
            if file_ext not in valid_video_extensions:
                QMessageBox.warning(self, "错误", "不支持的文件格式！请选择有效的视频文件。")
                return
            
            # 清理所有之前视频的数据
            self.cleanup_video_data()
            
            # 显示正在加载的提示
            self.status_label.setText("正在加载视频，请稍候...")
            self.status_label.setStyleSheet("color: blue;")
            QApplication.processEvents()  # 立即更新UI
            
            # 打开视频文件
            self.open_video(file_path)
            
            # 显示文件名
            file_name = os.path.basename(file_path)
            self.setWindowTitle(f"视频帧提取工具 - {file_name}")
            
            # 显示解码器信息
            if self.decoder:
                # 在预览区域上方显示解码器信息
                decoder_info = f"解码器: {self.decoder.decoder_type}"
                self.fps_label.setText(f"FPS: {self.fps:.2f} | {decoder_info}")
                
                # 在状态栏也显示解码器信息
                self.status_label.setText(f"正在生成预览图... | {decoder_info}")
                self.status_label.setStyleSheet("color: blue; font-weight: bold;")
                QApplication.processEvents()  # 立即更新UI
                
                # 检查是否有预览组标题并更新
                # 使用 hasattr 检查是否有预览组属性，避免错误
                if hasattr(self, 'preview_group'):
                    self.preview_group.setTitle(f"预览 ({self.decoder.decoder_type}解码)")
                
                # 确保有默认的预览设置
                if not hasattr(PreviewSettingsDialog, 'last_settings') or not PreviewSettingsDialog.last_settings:
                    # 创建默认设置
                    PreviewSettingsDialog.last_settings = {
                        'rows': 4,
                        'cols': 4,
                        'h_spacing': 10,
                        'v_spacing': 10,
                        'top_margin': 20,
                        'bottom_margin': 20,
                        'left_margin': 20,
                        'right_margin': 20,
                        'bg_color': QColor(255, 255, 255),
                        'show_video_info': True,
                        'info_font_size': 14,
                        'info_position': 'top'
                    }
                
                # 立即生成预览图，不使用定时器延迟
                try:
                    self.generate_preview(PreviewSettingsDialog.last_settings)
                    self.status_label.setText(f"视频已加载 - 使用 {self.decoder.decoder_type} 解码器")
                    self.status_label.setStyleSheet("color: green; font-weight: bold;")
                except Exception as e:
                    print(f"自动生成预览图失败: {str(e)}")
                    self.status_label.setText(f"视频已加载 - 使用 {self.decoder.decoder_type} 解码器 (预览图生成失败)")
                    self.status_label.setStyleSheet("color: orange; font-weight: bold;")
                
                # 5秒后清除状态信息
                QTimer.singleShot(5000, lambda: self.status_label.clear())

            # 获取并设置视频帧率
            self.fps = self.decoder.get_fps()
            self.fps_input.setValue(self.fps)  # 设置帧率输入框的值
            self.fps_label.setText(f"原始帧率: {self.fps:.3f} fps")

    def cleanup_video_data(self):
        """清理所有与视频相关的数据"""
        # 保存替换帧记录
        if hasattr(self, 'replaced_frames') and self.replaced_frames:
            self.save_replaced_frames()
            
        # 清理预览图相关数据
        self.current_preview_img = None
        self.display_preview_with_info = None
        self.preview_frame_positions = []
        
        # 清理替换帧相关数据
        if hasattr(self, 'replaced_frames'):
            self.replaced_frames = {}
        if hasattr(self, 'original_frame_mapping'):
            self.original_frame_mapping = {}
        
        # 清理帧缓存
        self.frame_buffer.clear()
        
        # 重置拖动状态
        self.is_dragging = False
        self.last_frame_pos = 0
        
        # 清理预览设置
        if hasattr(self, 'preview_dialog'):
            self.preview_dialog = None
        
        # 重置UI元素
        self.slider.setValue(0)
        self.fps_label.clear()
        self.status_label.clear()
        if hasattr(self, 'preview_group'):
            self.preview_group.setTitle("预览")
        
        # 重置窗口标题
        self.setWindowTitle("视频帧提取工具")
        
        # 清理解码器
        if hasattr(self, 'decoder'):
            self.decoder = None
        if hasattr(self, 'video_decoder'):
            self.video_decoder = None

    def on_size_change(self, text):
        is_custom = text == "自定义尺寸"
        self.width_input.setEnabled(is_custom)
        self.height_input.setEnabled(is_custom)
        
        # 如果是原始尺寸且视频已加载，设置为视频原始尺寸
        if text == "原始尺寸" and self.decoder and self.current_frame is not None:
            h, w = self.current_frame.shape[:2]
            self.width_input.setValue(w)
            self.height_input.setValue(h)
            
            # 触发预览图更新
            self.update_preview_delayed()

    def load_video(self):
        """加载视频文件"""
        try:
            # 释放旧的解码器
            if self.decoder:
                self.decoder.release()
                
            # 创建新的解码器
            self.decoder = VideoDecoder(self.video_path)
            self.total_frames = self.decoder.total_frames
            self.fps = self.decoder.fps
            
            # 更新界面
            self.fps_label.setText(f"原始: {self.fps:.3f}fps")
            self.slider.setEnabled(True)
            self.slider.setMinimum(0)
            self.slider.setMaximum(self.total_frames - 1)
            
            # 设置帧率输入框为视频原始帧率
            self.fps_input.setValue(self.fps)
            
            # 获取第一帧并设置尺寸
            frame = self.decoder.seek_frame(0)
            if frame is not None:
                self.current_frame = frame
                self.display_frame(frame)
                self.update_time_display(0)
                
                # 保存当前的尺寸设置
                current_width = self.width_input.value()
                current_height = self.height_input.value()
                current_size_mode = self.size_combo.currentText()
                
                # 获取原始尺寸
                original_width = frame.shape[1]
                original_height = frame.shape[0]
                
                # 如果之前没有设置过尺寸，才使用原始尺寸
                if current_width == 0 or current_height == 0:
                    self.width_input.setValue(original_width)
                    self.height_input.setValue(original_height)
                else:
                    # 保持用户之前设置的尺寸
                    self.width_input.setValue(current_width)
                    self.height_input.setValue(current_height)
                
                # 保持尺寸模式设置
                self.size_combo.setCurrentText(current_size_mode)
                
            # 启用所有相关按钮
            self.capture_btn.setEnabled(True)
            self.prev_btn.setEnabled(True)
            self.next_btn.setEnabled(True)
            self.make_gif_btn.setEnabled(True)
            self.clear_btn.setEnabled(True)
            
            # 确保快进退按钮可用
            self.prev_btn.setEnabled(True)
            self.next_btn.setEnabled(True)
            
            # 显示成功信息
            self.status_label.setText("视频加载成功")
            self.status_label.setStyleSheet("color: green;")
            QTimer.singleShot(3000, lambda: self.status_label.clear())
            
            # 视频加载成功后，自动生成预览图
            # 使用上次保存的预览设置，而不是默认设置
            if hasattr(PreviewSettingsDialog, 'last_settings'):
                self.generate_preview(PreviewSettingsDialog.last_settings)
            else:
                # 如果没有保存的设置，使用默认设置
                default_settings = {
                    'rows': 4,
                    'cols': 4,
                    'h_spacing': 10,
                    'v_spacing': 10,
                    'top_margin': 20,
                    'bottom_margin': 20,
                    'left_margin': 20,
                    'right_margin': 20
                }
                self.generate_preview(default_settings)
            
            # 更新总时间显示
            total_time = self.frame_to_timestamp(self.total_frames - 1)
            self.total_time_label.setText(total_time)
            
        except Exception as e:
            self.status_label.setText(f"加载视频失败: {str(e)}")
            self.status_label.setStyleSheet("color: red;")
            QTimer.singleShot(3000, lambda: self.status_label.clear())

    def init_keyframes_async(self):
        """异步初始化关键帧"""
        if self.keyframe_worker is not None:
            self.keyframe_worker.quit()
            self.keyframe_worker.wait()
            
        self.keyframe_worker = KeyframeInitWorker(self.video_path, self.fps)
        self.keyframe_worker.finished.connect(self.on_keyframes_ready)
        self.keyframe_worker.start()
        
        # 显示加载提示
        self.status_label.setText("正在加载视频关键帧...")
        self.status_label.setStyleSheet("color: #2196F3;")

    def on_keyframes_ready(self, keyframes):
        """当关键帧初始化完成时"""
        self.keyframe_positions = keyframes
        self.status_label.setText("视频加载完成")
        self.status_label.setStyleSheet("color: #4CAF50;")
        QTimer.singleShot(2000, lambda: self.status_label.clear())
        
        # 清理worker
        if self.keyframe_worker:
            self.keyframe_worker.deleteLater()
            self.keyframe_worker = None

    def on_frame_loaded(self, frame):
        """当帧加载完成时更新显示"""
        if self.current_frame is None:  # 只更新第一帧
            self.current_frame = frame
            self.display_frame(frame)
            
    def on_video_loaded(self):
        """视频加载完成后的处理"""
        self.progress_dialog.close()
        if self.load_worker:
            self.load_worker.deleteLater()
            self.load_worker = None
        
        self.cap = cv2.VideoCapture(self.video_path)
        self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
        self.fps = self.cap.get(cv2.CAP_PROP_FPS)
        
        # 清空帧缓存
        self.frame_buffer.clear()
        
        self.slider.setEnabled(True)
        self.slider.setMinimum(0)
        self.slider.setMaximum(self.total_frames - 1)
        
        self.capture_btn.setEnabled(True)
        self.update_frame(0)
        
        self.prev_btn.setEnabled(True)
        self.next_btn.setEnabled(True)
        self.make_gif_btn.setEnabled(True)
        
        # 初始预加载
        self.preload_frames(0, self.preload_range)
        
    def preload_frames(self, start_pos, count):
        # 异步预加载更多帧
        for pos in range(start_pos, start_pos + count):
            if self.video_decoder and not self.frame_buffer.get(pos):
                frame = self.video_decoder.seek_frame(pos)
                if frame is not None:
                    self.frame_buffer.add(pos, frame)
        
        # 清理远处的缓存
        current_pos = self.slider.value()
        keys_to_remove = []
        for pos in self.frame_buffer.keys():
            if abs(pos - current_pos) > self.buffer_size:
                keys_to_remove.append(pos)
        for key in keys_to_remove:
            del self.frame_buffer[key]
            
    def slider_changed(self):
        """滑块值改变时的处理"""
        if not self.decoder:
            return
            
        frame_pos = self.slider.value()
        current_time = time.time() * 1000
        
        # 始终更新时间显示
        self.update_time_display(frame_pos)
        
        # 对于起始帧或结束帧，强制更新显示
        if frame_pos == 0 or frame_pos == self.total_frames - 1:
            frame = self.decoder.seek_frame(frame_pos)
            if frame is not None:
                self.display_frame(frame)
                self.last_seek_time = current_time
            return
        
        # 对其他帧的获取和显示进行频率限制
        if current_time - self.last_seek_time >= self.seek_interval:
            frame = self.decoder.seek_frame(frame_pos)
            if frame is not None:
                self.display_frame(frame)
                self.last_seek_time = current_time

    def find_nearest_keyframe(self, target_pos):
        """找到最近的关键帧"""
        if not self.keyframe_positions:
            return None
            
        # 二分查找最近的关键帧
        left = 0
        right = len(self.keyframe_positions) - 1
        
        while left <= right:
            mid = (left + right) // 2
            if self.keyframe_positions[mid] == target_pos:
                return self.keyframe_positions[mid]
            elif self.keyframe_positions[mid] < target_pos:
                left = mid + 1
            else:
                right = mid - 1
        
        # 找到最近的关键帧
        if right < 0:
            return self.keyframe_positions[0]
        if left >= len(self.keyframe_positions):
            return self.keyframe_positions[-1]
            
        # 比较两个相邻关键帧，返回较近的一个
        prev_diff = abs(target_pos - self.keyframe_positions[right])
        next_diff = abs(target_pos - self.keyframe_positions[left])
        return self.keyframe_positions[right] if prev_diff < next_diff else self.keyframe_positions[left]

    def seek_to_frame(self, frame_pos):
        """优化的帧定位方法"""
        if frame_pos in self.frame_buffer:
            self.display_frame(self.frame_buffer[frame_pos])
            return True
            
        # 设置帧位置并读取
        self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_pos)
        ret, frame = self.cap.read()
        if ret:
            # 更新缓存
            self.frame_buffer[frame_pos] = frame
            # 维护缓存大小
            if len(self.frame_buffer) > self.buffer_size:
                # 删除最远的帧
                sorted_positions = sorted(self.frame_buffer.keys())
                current_pos = frame_pos
                while len(self.frame_buffer) > self.buffer_size:
                    furthest_pos = max(sorted_positions, 
                                     key=lambda x: abs(x - current_pos))
                    del self.frame_buffer[furthest_pos]
                    sorted_positions.remove(furthest_pos)
            
            self.display_frame(frame)
            return True
        return False

    def smooth_update(self):
        """平滑更新显示"""
        if not self.is_seeking or not self.cap:
            self.smooth_timer.stop()
            return
            
        current_pos = self.slider.value()
        if abs(current_pos - self.seek_target) <= 1:
            self.is_seeking = False
            self.smooth_timer.stop()
            self.seek_to_frame(self.seek_target)
            return
            
        # 计算下一个要显示的帧位置
        step = (self.seek_target - current_pos) // 2
        next_pos = current_pos + step
        
        # 更新滑块位置
        self.slider.setValue(next_pos)
        
        # 尝试从缓存中获取帧
        if next_pos in self.frame_buffer:
            self.display_frame(self.frame_buffer[next_pos])

    def update_frame_fast(self, frame_pos):
        """快速更新帧（用于拖动）"""
        if self.cap is None:
            return
            
        # 确保帧位置有效
        frame_pos = max(0, min(frame_pos, self.total_frames - 1))
        
        # 尝试从缓存读取
        if frame_pos in self.frame_buffer:
            self.display_frame(self.frame_buffer[frame_pos])
            return
            
        # 读取新帧
        self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_pos)
        ret, frame = self.cap.read()
        if ret:
            # 更新缓存
            self.frame_buffer[frame_pos] = frame
            # 保持缓存大小
            if len(self.frame_buffer) > self.buffer_size:
                self.frame_buffer.clear()  # 直接清空缓存
            self.display_frame(frame)
            
    def display_frame(self, frame):
        """优化的帧显示方法"""
        if frame is None:
            return
        
        self.current_frame = frame
        
        # 获取显示区域大小
        label_size = self.image_label.size()
        label_w = label_size.width()
        label_h = label_size.height()
        
        # 如果尺寸太小，不进行处理
        if label_w <= 10 or label_h <= 10:
            return
            
        # 获取帧尺寸
        h, w = frame.shape[:2]
        
        # 计算缩放比例，保持宽高比
        scale_w = label_w / w
        scale_h = label_h / h
        scale = min(scale_w, scale_h)
        
        # 计算新尺寸
        new_w = int(w * scale)
        new_h = int(h * scale)
        
        # 转换颜色空间并缩放
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        if scale != 1.0:
            frame_rgb = cv2.resize(frame_rgb, (new_w, new_h), 
                             interpolation=cv2.INTER_AREA)
        
        # 创建 QImage
        bytes_per_line = frame_rgb.shape[2] * frame_rgb.shape[1]
        q_img = QImage(frame_rgb.data, frame_rgb.shape[1], frame_rgb.shape[0], 
                      bytes_per_line, QImage.Format.Format_RGB888)
        
        # 创建 QPixmap 并直接设置
        pixmap = QPixmap.fromImage(q_img)
        self.image_label.setPixmap(pixmap)
        
        # 设置图像标签的大小策略为保持宽高比
        self.image_label.setScaledContents(False)
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

    def on_width_changed(self, width):
        """当宽度改变时自动计算高度"""
        # 修改判断条件，正确处理 numpy 数组
        if not hasattr(self, 'decoder') or not self.decoder or self.current_frame is None or not self.width_input.isEnabled():
            return
            
        # 获取当前帧
        current_frame = self.current_frame
        if current_frame is None:
            return
            
        # 获取原始宽高比
        original_width = current_frame.shape[1]
        original_height = current_frame.shape[0]
        ratio = original_height / original_width
        
        # 如果宽度有效，自动计算高度
        if width > 0:
            new_height = int(width * ratio)
            # 阻止信号循环
            self.height_input.blockSignals(True)
            self.height_input.setValue(new_height)
            self.height_input.blockSignals(False)
        elif width == 0:
            # 如果宽度为0，高度也设为0
            self.height_input.blockSignals(True)
            self.height_input.setValue(0)
            self.height_input.blockSignals(False)
        
        # 触发预览图更新
        self.update_preview_delayed()

    def on_height_changed(self, height):
        """当高度改变时自动计算宽度"""
        # 修改判断条件，正确处理 numpy 数组
        if not hasattr(self, 'decoder') or not self.decoder or self.current_frame is None or not self.height_input.isEnabled():
            return
            
        # 获取当前帧
        current_frame = self.current_frame
        if current_frame is None:
            return
            
        # 获取原始宽高比
        original_width = current_frame.shape[1]
        original_height = current_frame.shape[0]
        ratio = original_width / original_height
        
        # 如果高度有效，自动计算宽度
        if height > 0:
            new_width = int(height * ratio)
            # 阻止信号循环
            self.width_input.blockSignals(True)
            self.width_input.setValue(new_width)
            self.width_input.blockSignals(False)
        elif height == 0:
            # 如果高度为0，宽度也设为0
            self.width_input.blockSignals(True)
            self.width_input.setValue(0)
            self.width_input.blockSignals(False)
        
        # 触发预览图更新
        self.update_preview_delayed()

    def capture_frame(self):
        """截取当前帧"""
        if self.current_frame is None:
            return
            
        try:
            # 获取保存路径
            save_dir = self.path_input.text()
            
            # 如果选择了保存到视频目录且有视频路径
            if hasattr(self, 'save_to_video_dir_checkbox') and self.save_to_video_dir_checkbox.isChecked() and hasattr(self, 'video_path') and self.video_path:
                save_dir = os.path.dirname(os.path.abspath(self.video_path))
            
            # 获取当前时间作为文件名
            current_time = self.frame_to_timestamp(self.slider.value()).replace(':', '-')
            video_name = Path(self.video_path).stem if self.video_path else "capture"
            
            # 清理文件名，移除不合法字符
            video_name = re.sub(r'[\\/*?:"<>|]', "_", video_name)
            
            # 添加时间戳确保文件名唯一
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            
            # 获取用户设置的保存格式
            format_type = self.save_format_combo.currentText().lower()
            filename = f"{video_name}_{timestamp}_{current_time}.{format_type}"
            
            # 使用 os.path.join 确保路径分隔符正确
            save_path = os.path.join(save_dir, filename)
            save_path = os.path.normpath(save_path)  # 再次规范化完整路径
            
            # 确保目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            success = False
            
            # 对于PNG和JPG格式，优先使用FFMPEG实现无损/高质量输出
            if (format_type == 'png' or format_type == 'jpg') and hasattr(self, 'video_path') and self.video_path:
                try:
                    # 获取当前帧位置
                    frame_pos = self.slider.value()
                    frame_time = frame_pos / self.fps
                    
                    # 获取ffmpeg路径
                    ffmpeg_path = get_ffmpeg_path()
                    
                    # 构建基本FFMPEG命令
                    cmd = [
                        ffmpeg_path,
                        "-ss", str(frame_time),
                        "-i", self.video_path,
                        "-frames:v", "1"
                    ]
                    
                    # 根据格式添加特定参数
                    if format_type == 'png':
                        # PNG无损参数
                        cmd.extend([
                            "-pix_fmt", "rgba",  # 使用RGBA格式，保留alpha通道
                            "-compression_level", "0"  # 无压缩
                        ])
                    else:  # JPG
                        # JPG最高质量参数
                        cmd.extend([
                            "-q:v", "1",          # 质量级别1（最高）
                            "-qscale:v", "1",     # 质量比例1
                            "-compression_level", "0",  # 压缩级别0（最低压缩）
                            "-pix_fmt", "yuvj444p"  # 使用4:4:4采样（无色彩子采样）
                        ])
                    
                    # 添加输出路径
                    cmd.append("-y")
                    cmd.append(save_path)
                    
                    # 如果设置了自定义尺寸
                    if self.size_combo.currentText() == "自定义尺寸":
                        width = self.width_input.value()
                        height = self.height_input.value()
                        if width > 0 and height > 0:
                            cmd.insert(-2, "-s")
                            cmd.insert(-2, f"{width}x{height}")
                    
                    # 执行命令
                    print(f"执行FFmpeg {format_type.upper()}截图命令: {' '.join(cmd)}")
                    process = subprocess.run(
                        cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=False,  # 修改为 False，不尝试解码
                        creationflags=subprocess.CREATE_NO_WINDOW
                    )
                    
                    if process.returncode != 0:
                        print(f"FFmpeg截图失败: {process.stderr.decode('utf-8', errors='ignore')}")
                        raise Exception(f"FFmpeg截图失败")
                    
                    success = os.path.exists(save_path)
                    if not success:
                        raise Exception(f"FFmpeg未能生成截图: {save_path}")
                    
                except Exception as ffmpeg_err:
                    print(f"使用FFmpeg截取{format_type.upper()}失败，回退到PIL方式: {str(ffmpeg_err)}")
                    # 回退到使用PIL保存
                    success = False
            
            # 如果FFMPEG方式失败或者是其他格式，使用PIL保存图片
            if not success:
                # 获取当前帧
                frame = self.current_frame.copy()  # 创建副本避免修改原始帧
                
                # 检查帧是否有效
                if frame is None or frame.size == 0:
                    raise Exception("无效的视频帧")
                
                # 应用尺寸设置（如果需要）
                if self.size_combo.currentText() == "自定义尺寸":
                    width = self.width_input.value()
                    height = self.height_input.value()
                    if width > 0 and height > 0:
                        frame = cv2.resize(frame, (width, height), 
                                         interpolation=cv2.INTER_LANCZOS4)
                    
                # 尝试使用PIL保存图片
                try:
                    from PIL import Image
                    img = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
                    
                    # 根据格式选择保存参数
                    if format_type == 'jpg':
                        if img.mode != 'RGB':
                            img = img.convert('RGB')
                        img.save(save_path, 
                                format='JPEG',
                                quality=self.jpg_quality_spin.value(),  # 使用用户设置的质量
                                optimize=True,
                                progressive=True,
                                subsampling=0  # 使用4:4:4子采样，提高质量
                        )
                    else:  # PNG格式
                        img.save(save_path, format='PNG', optimize=True)
                        
                    success = os.path.exists(save_path)
                except:
                    # 如果PIL失败，回退到OpenCV并使用支持中文路径的方法
                    success = cv2_imwrite_chinese(save_path, frame)
            
            if not success or not os.path.exists(save_path):
                raise Exception(f"无法保存图片到 {save_path}")
                
            # 显示成功消息
            self.status_label.setText(f"截图已保存到: {save_path}")
            self.status_label.setStyleSheet("color: green;")
            QTimer.singleShot(5000, lambda: self.status_label.clear())
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"截图失败: {str(e)}")
            print(f"截图失败: {str(e)}")
            traceback.print_exc()

    def frame_to_timestamp(self, frame_pos):
        """将帧位置转换为时间戳字符串"""
        if self.fps <= 0:
            return "00:00:00"
        
        total_seconds = frame_pos / self.fps
        hours = int(total_seconds // 3600)
        minutes = int((total_seconds % 3600) // 60)
        seconds = int(total_seconds % 60)
        frames = int((total_seconds * self.fps) % self.fps)
        
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}.{frames:02d}"

    def browse_path(self):
        dir_path = QFileDialog.getExistingDirectory(self, "选择保存目录", self.path_input.text())
        if dir_path:
            self.path_input.setText(dir_path)
            
    def start_forward(self):
        """开始快进"""
        if not self.decoder:
            return
        self.seek_direction = 1
        # 使用固定的更新间隔
        self.seek_timer.start(50)  # 降低间隔到50ms使移动更流畅

    def start_backward(self):
        """开始快退"""
        if not self.decoder:
            return
        self.seek_direction = -1
        # 使用固定的更新间隔
        self.seek_timer.start(50)  # 降低间隔到50ms使移动更流畅

    def stop_seeking(self):
        """停止快进退"""
        self.seek_timer.stop()
        # 直接使用当前帧，不再重新获取
        self.update_time_display(self.slider.value())

    def continuous_seek(self):
        """处理连续快进退"""
        if not self.decoder:
            return
    
        current_pos = self.slider.value()
        seek_seconds = self.seek_input.value()
        
        # 计算需要移动的帧数
        frames_to_move = int(seek_seconds * self.fps)
        if seek_seconds == 0.1:  # 如果是0.1秒
            frames_to_move = max(1, int(0.1 * self.fps))
        
        # 计算新位置
        new_pos = current_pos + (frames_to_move * self.seek_direction)
        new_pos = max(0, min(new_pos, self.total_frames - 1))
        
        # 更新滑块位置
        self.slider.setValue(new_pos)
        
        # 使用OpenCV定位到指定帧
        if self.decoder.cap and self.decoder.cap.isOpened():
            self.decoder.cap.set(cv2.CAP_PROP_POS_FRAMES, new_pos)
            ret, frame = self.decoder.cap.read()
            if ret:
                # 更新缓存
                self.decoder.frame_buffer[new_pos] = frame
                # 限制缓存大小
                if len(self.decoder.frame_buffer) > self.decoder.buffer_size:
                    self.decoder.frame_buffer.popitem(last=False)
                self.decoder.current_pos = new_pos
                
                # 显示帧
                self.display_frame(frame)
                self.update_time_display(new_pos)

    def goto_time(self):
        """跳转到指定时间"""
        if not self.decoder:
            return
    
        # 计算目标帧位置
        hours = self.hour_input.value()
        minutes = self.minute_input.value()
        seconds = self.second_input.value()
        frames = self.frame_input.value()
    
        # 计算总秒数
        total_seconds = hours * 3600 + minutes * 60 + seconds + frames / self.fps
    
        # 计算目标帧号
        target_frame = int(total_seconds * self.fps)
    
        # 确保帧号在有效范围内
        target_frame = max(0, min(target_frame, self.total_frames - 1))
    
        # 更新滑块位置并立即显示帧
        self.slider.setValue(target_frame)
        frame = self.decoder.seek_frame(target_frame)  # 修改为 seek_frame
        if frame is not None:
            self.display_frame(frame)

    def create_animation(self):
        """创建动画，支持多个位置和拼接模式"""
        if not self.decoder or self.current_frame is None:
            QMessageBox.warning(self, "错误", "请先打开视频文件")
            return

        try:
            # 获取所有位置
            positions = []
            for i in range(self.position_list.count()):
                item = self.position_list.item(i)
                frame_pos = item.data(Qt.ItemDataRole.UserRole)
                positions.append(frame_pos)
            
            # 如果没有保存的位置，使用当前位置
            if not positions:
                positions = [self.slider.value()]
            
            # 准备参数
            params = self.prepare_animation_params()
            if not params:
                return
            
            # 设置进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 100)
            self.progress_bar.setValue(0)
            
            # 禁用相关按钮
            self.make_gif_btn.setEnabled(False)
            self.add_position_btn.setEnabled(False)
            self.remove_position_btn.setEnabled(False)
            self.clear_positions_btn.setEnabled(False)
            self.cancel_btn.setEnabled(True)
            
            # 根据拼接模式选择处理方法
            if self.stitch_combo.currentText() == "横向拼接":
                # 创建并启动拼接工作线程
                self.stitch_worker = StitchWorker(positions, params, self.decoder, self.fps)
                self.stitch_worker.progress_signal.connect(self.update_stitch_progress)
                self.stitch_worker.finished_signal.connect(self.handle_stitch_result)
                self.stitch_worker.start()
            else:
                # 使用原有的批处理方式
                self.batch_manager = BatchManager(positions, self)
                self.batch_manager.start_next()
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"创建动态图时出错:\n{str(e)}")
            self.cleanup_animation_ui()

    def start_dragging(self):
        """开始拖动"""
        self.is_dragging = True
        self.last_frame_pos = self.slider.value()
        self.last_update_time = QTime.currentTime()
        self.frame_buffer.clear()  # 清空缓存开始新的拖动
        
    def stop_dragging(self):
        """停止拖动"""
        self.is_dragging = False
        self.frame_buffer.clear()  # 清空缓存
        self.update_frame(self.slider.value())
    def keyPressEvent(self, event):
        """处理键盘事件，支持左右方向键控制快进和快退"""
        if not self.decoder or self.total_frames <= 0:
            return super().keyPressEvent(event)
            
        if event.key() == Qt.Key.Key_Left and not event.isAutoRepeat():
            # 左方向键，开始连续后退
            self.seek_direction = -1
            self.seek_timer.start(50)  # 同按钮一样的更新频率
        elif event.key() == Qt.Key.Key_Right and not event.isAutoRepeat():
            # 右方向键，开始连续前进
            self.seek_direction = 1
            self.seek_timer.start(50)  # 同按钮一样的更新频率
        else:
            super().keyPressEvent(event)
            
    def keyReleaseEvent(self, event):
        """处理键盘释放事件，停止连续播放"""
        if (event.key() == Qt.Key.Key_Left or event.key() == Qt.Key.Key_Right) and not event.isAutoRepeat():
            # 停止连续播放
            self.seek_timer.stop()
            self.update_time_display(self.slider.value())
        else:
            super().keyReleaseEvent(event)

        
    def update_frame(self, frame_pos):
        """更新帧"""
        if not self.decoder:
            return
            
        frame_pos = max(0, min(frame_pos, self.total_frames - 1))
        frame = self.decoder.seek_frame(frame_pos)  # 修改为 seek_frame
        if frame is not None:
            self.display_frame(frame)
            self.update_time_display(frame_pos)

    def clear_video(self):
        """清空当前视频"""
        if self.decoder:
            # 如果是OpenCV解码器，调用release
            if hasattr(self.decoder, 'cap') and self.decoder.cap:
                self.decoder.cap.release()
            # 如果是PyAV解码器，关闭container
            if hasattr(self.decoder, 'container') and self.decoder.container:
                self.decoder.container.close()
            self.decoder = None
            
        # 重置界面
        self.video_path = None
        self.total_frames = 0
        self.current_frame = None
        self.fps = 0
        self.frame_buffer.clear()
        
        # 重置显示
        self.image_label.setText("将视频文件拖放到这里")
        self.image_label.setPixmap(QPixmap())
        
        # 禁用控件
        self.slider.setEnabled(False)
        self.slider.setValue(0)
        self.capture_btn.setEnabled(False)
        self.prev_btn.setEnabled(False)
        self.next_btn.setEnabled(False)
        self.make_gif_btn.setEnabled(False)
        self.clear_btn.setEnabled(False)
        
        # 清除帧率显示
        self.fps_label.clear()
        self.fps_input.setValue(24.0)  # 重置为默认值
        self.fps_label.setText("")  # 清空原始帧率显示

    def update_time_display(self, frame_pos):
        """更新时间显示，同时更新时间输入框"""
        if self.fps > 0 and self.total_frames > 0:
            # 计算当前时间
            current_seconds = frame_pos / self.fps
            current_hours = int(current_seconds // 3600)
            current_minutes = int((current_seconds % 3600) // 60)
            current_seconds = int(current_seconds % 60)
            # 直接从帧位置计算帧数，而不是从秒数计算
            frames = int(frame_pos % self.fps)
            
            # 计算总时长
            total_seconds = self.total_frames / self.fps
            total_hours = int(total_seconds // 3600)
            total_minutes = int((total_seconds % 3600) // 60)
            total_seconds = int(total_seconds % 60)
            
            # 更新时间标签，显示当前时间/总时长（包含帧数）
            current_time = f"{current_hours:02d}:{current_minutes:02d}:{current_seconds:02d}:{frames:02d}"
            total_time = f"{total_hours:02d}:{total_minutes:02d}:{total_seconds:02d}:00"
            self.time_label.setText(current_time)
            self.total_time_label.setText(total_time)  # 更新总时长标签
            
            # 更新时间输入框
            self.hour_input.setValue(current_hours)
            self.minute_input.setValue(current_minutes)
            self.second_input.setValue(current_seconds)
            self.frame_input.setValue(frames)
            
            # 更新帧率显示
            self.fps_label.setText(f"fps: {self.fps:.2f}")

    def add_current_position(self):
        """添加当前位置到列表"""
        if not self.decoder:
            return
        
        current_pos = self.slider.value()
        
        # 检查是否已存在该位置
        for i in range(self.position_list.count()):
            existing_pos = self.position_list.item(i).data(Qt.ItemDataRole.UserRole)
            if abs(existing_pos - current_pos) < 5:  # 允许5帧的误差
                self.status_label.setText("该位置附近已存在标记点")
                self.status_label.setStyleSheet("color: orange;")
                QTimer.singleShot(2000, lambda: self.status_label.clear())
                return
        
        # 获取当前帧的时间戳
        timestamp = self.frame_to_timestamp(current_pos)
        
        # 创建新的列表项
        item_text = f"位置 {self.position_list.count() + 1}: {timestamp} (帧: {current_pos})"
        item = QListWidgetItem(item_text)
        item.setData(Qt.ItemDataRole.UserRole, current_pos)
        
        # 添加到列表
        self.position_list.addItem(item)
        self.position_list.update_item_numbers()  # 更新所有项的编号
        
        # 显示成功提示
        self.status_label.setText(f"已添加位置: {timestamp}")
        self.status_label.setStyleSheet("color: green;")
        QTimer.singleShot(2000, lambda: self.status_label.clear())
        
        # 将焦点设回主窗口，确保键盘左右键可以正常使用
        self.setFocus()

    def remove_selected_position(self):
        """删除选中的位置"""
        selected_items = self.position_list.selectedItems()
        for item in selected_items:
            self.position_list.takeItem(self.position_list.row(item))
        
        # 将焦点设回主窗口，确保键盘左右键可以正常使用
        self.setFocus()

    def clear_positions(self):
        """清空所有位置"""
        self.position_list.clear()
        
        # 将焦点设回主窗口，确保键盘左右键可以正常使用
        self.setFocus()

    def load_settings(self):
        """从文件加载设置"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    
                # 应用加载的设置
                if 'output_path' in settings:
                    self.path_input.setText(settings['output_path'])
                if 'quality_mode' in settings:
                    self.quality_combo.setCurrentText(settings['quality_mode'])
                if 'duration' in settings:
                    self.duration_input.setValue(int(settings['duration']))
                if 'fps' in settings:
                    self.fps_input.setValue(float(settings['fps']))
                if 'size_mode' in settings:
                    self.size_combo.setCurrentText(settings['size_mode'])
                if 'width' in settings:
                    self.width_input.setValue(int(settings['width']))
                if 'height' in settings:
                    self.height_input.setValue(int(settings['height']))
                if 'seek_time' in settings:
                    self.seek_input.setValue(float(settings['seek_time']))
                if 'save_format' in settings:
                    self.save_format_combo.setCurrentText(settings['save_format'])
                if 'jpg_quality' in settings:
                    self.jpg_quality_spin.setValue(int(settings['jpg_quality']))
                if 'stitch_mode' in settings:
                    self.stitch_combo.setCurrentText(settings['stitch_mode'])
                if 'save_to_video_dir' in settings:
                    self.save_to_video_dir_checkbox.setChecked(settings['save_to_video_dir'])
                
                print(f"已加载设置: {self.settings_file}")
            else:
                self.apply_default_settings()
                
        except Exception as e:
            print(f"加载设置时出错: {e}")
            self.apply_default_settings()

    def save_settings(self):
        """保存设置到文件"""
        try:
            settings = {
                'quality_mode': self.quality_combo.currentText(),
                'output_path': self.path_input.text(),
                'duration': self.duration_input.value(),
                'fps': self.fps_input.value(),
                'size_mode': self.size_combo.currentText(),
                'width': self.width_input.value(),
                'height': self.height_input.value(),
                'seek_time': self.seek_input.value(),
                'save_format': self.save_format_combo.currentText(),
                'jpg_quality': self.jpg_quality_spin.value(),
                'stitch_mode': self.stitch_combo.currentText(),
                'save_to_video_dir': self.save_to_video_dir_checkbox.isChecked()  # 添加新设置项
            }
            
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=4)
                
        except Exception as e:
            print(f"保存设置时出错: {e}")

    def apply_default_settings(self):
        """应用默认设置"""
        self.path_input.setText(self.default_settings['save_path'])
        self.size_combo.setCurrentText(self.default_settings['size_mode'])
        self.width_input.setValue(self.default_settings['width'])
        self.height_input.setValue(self.default_settings['height'])
        self.seek_input.setValue(self.default_settings['seek_time'])
        self.duration_input.setValue(int(self.default_settings['duration']))
        self.fps_input.setValue(self.default_settings['fps'])
        self.quality_combo.setCurrentText(self.default_settings['quality_mode'])
        self.stitch_combo.setCurrentText(self.default_settings['stitch_mode'])
        
        # 设置保存到视频目录的复选框状态
        if 'save_to_video_dir' in self.default_settings:
            self.save_to_video_dir_checkbox.setChecked(self.default_settings['save_to_video_dir'])

    def closeEvent(self, event):
        """窗口关闭时保存设置"""
        self.save_settings()
        event.accept()

    def prepare_animation_params(self, frame_pos=None):
        """修改参数准备方法以支持新的解码器"""
        if frame_pos is None:
            frame_pos = self.slider.value()  # 使用当前进度条位置
    
        if not self.decoder or not self.video_path:
            raise Exception("请先打开视频文件")
    
        try:
            # 使用传入的帧位置作为起始点
            start_frame = frame_pos
            duration = self.duration_input.value()
            target_fps = self.fps_input.value()
            total_frames = int(duration * target_fps)
            
            # 计算精确的开始时间点（秒）
            exact_start_time = start_frame / self.fps
            
            # 准备保存路径
            format_type = 'webp'  # 直接使用 webp 格式
            
            # 使用 Qt 的 QFileInfo 处理文件名
            file_info = QFileInfo(self.video_path)
            video_name = file_info.completeBaseName()
            
            # 添加时间戳和帧位置到文件名
            current_time = QTime.currentTime().toString("HHmmss")
            current_date = QDate.currentDate().toString("yyyyMMdd")
            frame_timestamp = self.frame_to_timestamp(start_frame).replace(":", "-")
            
            # 构建文件名
            file_name = f"{video_name}_{current_date}_{current_time}_frame{start_frame}_{frame_timestamp}.{format_type}"
            
            # 使用 Qt 的路径处理
            save_dir = QDir(self.path_input.text())
            if not save_dir.exists():
                save_dir.mkpath(".")
            
            # 获取完整路径
            save_path = save_dir.absoluteFilePath(file_name)
            
            # 获取尺寸设置
            if self.size_combo.currentText() == "自定义尺寸":
                target_width = self.width_input.value()
                target_height = self.height_input.value()
            else:
                # 使用原始尺寸
                frame = self.decoder.seek_frame(start_frame)
                if frame is not None:
                    target_width = frame.shape[1]
                    target_height = frame.shape[0]
                else:
                    raise Exception("无法获取视频尺寸")
            
            # 获取循环设置
            loop_count = 0  # 始终使用无限循环
            
            # 获取质量模式
            quality_mode = self.quality_combo.currentText()
            
            # 创建并返回参数字典
            return {
                'video_path': self.video_path,
                'start_frame': start_frame,
                'total_frames': total_frames,
                'video_total_frames': self.total_frames,
                'target_fps': target_fps,
                'duration': duration,
                'format_type': format_type,
                'save_path': save_path,
                'loop_count': loop_count,  # 始终为0（无限循环）
                'resize': self.size_combo.currentText() == "自定义尺寸",
                'target_width': target_width,
                'target_height': target_height,
                'quality_mode': quality_mode,
                'decoder': self.decoder,
                'exact_start_time': exact_start_time  # 精确的开始时间点
            }
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"准备参数时出错：{str(e)}")
            return None

    def cancel_animation(self):
        """取消动画制作"""
        try:
            if hasattr(self, 'stitch_worker') and self.stitch_worker and self.stitch_worker.isRunning():
                self.stitch_worker.is_cancelled = True
                self.status_label.setText("正在取消...")
                self.status_label.setStyleSheet("color: orange;")
            elif hasattr(self, 'batch_manager') and self.batch_manager:
                self.batch_manager.cancel()  # 使用新的 cancel 方法
                self.status_label.setText("正在取消...")
                self.status_label.setStyleSheet("color: orange;")
        except Exception as e:
            print(f"取消操作时出错: {str(e)}")

    def load_quality_setting(self):
        """加载质量模式设置"""
        try:
            # 检查settings_file是否已经初始化
            if not hasattr(self, 'settings_file') or not self.settings_file:
                # 设置文件路径
                app_data_dir = os.path.join(os.getenv('APPDATA'), 'VideoCapture')
                if not os.path.exists(app_data_dir):
                    os.makedirs(app_data_dir)
                self.settings_file = os.path.join(app_data_dir, 'settings.json')
                print(f"已初始化设置文件路径: {self.settings_file}")
                
            if hasattr(self, 'quality_combo') and os.path.exists(self.settings_file):
                try:
                    with open(self.settings_file, 'r', encoding='utf-8') as f:
                        settings = json.load(f)
                        quality_mode = settings.get('quality_mode', "平衡")  # 默认使用平衡模式
                        self.quality_combo.setCurrentText(quality_mode)
                except json.JSONDecodeError:
                    print("质量设置文件格式错误，使用默认设置")
            elif hasattr(self, 'quality_combo'):
                print("使用默认质量设置: 平衡")
                self.quality_combo.setCurrentText("平衡")  # 默认使用平衡模式
        except Exception as e:
            print(f"加载质量设置时出错: {e}")
            import traceback
            traceback.print_exc()

    # 在各个设置改变的地方也可以调用保存
    def on_setting_changed(self):
        """设置改变时保存"""
        self.save_settings()

    def toggle_window_top(self):
        """切换窗口置顶状态"""
        self.is_top = not self.is_top
        if self.is_top:
            self.setWindowFlags(self.windowFlags() | Qt.WindowType.WindowStaysOnTopHint)
            self.status_label.setText("窗口已置顶")
            self.top_window_btn.setChecked(True)
        else:
            self.setWindowFlags(self.windowFlags() & ~Qt.WindowType.WindowStaysOnTopHint)
            self.status_label.setText("窗口已取消置顶")
            self.top_window_btn.setChecked(False)
        
        # 重新显示窗口
        self.show()
        # 3秒后清除状态信息
        QTimer.singleShot(3000, lambda: self.status_label.clear())

    def on_save_format_changed(self, format_text):
        """当保存格式改变时显示/隐藏质量设置"""
        self.jpg_quality_spin.setVisible(format_text == "JPG")

    def open_video(self, video_path):
        """改进的视频打开方法，加强自动刷新预览图功能"""
        try:
            # 检查文件扩展名
            file_ext = os.path.splitext(video_path)[1].lower()
            valid_video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm']
            if file_ext not in valid_video_extensions:
                QMessageBox.warning(self, "错误", "不支持的文件格式！请选择有效的视频文件。")
                return
            
            # 使用新的解码器类
            self.decoder = VideoDecoder(video_path)
            
            # 获取视频信息
            self.fps = self.decoder.fps
            self.total_frames = self.decoder.total_frames
            
            # 更新界面
            self.video_path = video_path
            self.slider.setMaximum(self.total_frames - 1)
            self.slider.setValue(0)
            self.slider.setEnabled(True)
            
            # 启用相关按钮
            self.capture_btn.setEnabled(True)
            self.prev_btn.setEnabled(True)
            self.next_btn.setEnabled(True)
            self.make_gif_btn.setEnabled(True)
            self.clear_btn.setEnabled(True)
            
            # 显示第一帧
            frame = self.decoder.seek_frame(0)
            if frame is not None:
                self.current_frame = frame
                self.display_frame(frame)
                self.update_time_display(0)
            
            # 显示视频信息和解码器类型
            self.fps_label.setText(f"fps: {self.fps:.2f} | 解码器: {self.decoder.decoder_type}")
            
            # 在状态栏显示解码器信息
            self.status_label.setText(f"视频已加载 - 使用 {self.decoder.decoder_type} 解码器")
            self.status_label.setStyleSheet("color: green;")
            
            # 清空帧缓存
            self.frame_buffer.clear()
            
            # 尝试自动生成预览图（不通过定时器，直接调用）
            if hasattr(PreviewSettingsDialog, 'last_settings') and PreviewSettingsDialog.last_settings:
                try:
                    self.generate_preview(PreviewSettingsDialog.last_settings)
                except Exception as e:
                    print(f"自动生成预览图失败: {str(e)}")
            
            QTimer.singleShot(3000, lambda: self.status_label.clear())
            
            # 获取视频的原始帧率并更新显示
            self.fps = self.decoder.get_fps()
            self.fps_input.setValue(self.fps)  # 设置帧率输入框的值为视频原始帧率
            self.fps_label.setText(f"原始帧率: {self.fps:.3f} fps")  # 显示原始帧率
            
            # 如果选中了保存到视频目录选项，则更新保存路径
            if hasattr(self, 'save_to_video_dir_checkbox') and self.save_to_video_dir_checkbox.isChecked():
                self.update_save_path()
            
            # 加载替换帧记录
            self.load_replaced_frames()
            
            # 在成功打开视频后加载预览设置
            self.load_preview_settings()
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"打开视频文件失败：{str(e)}")
            self.clear_video()

    def resizeEvent(self, event):
        """处理窗口大小改变事件"""
        super().resizeEvent(event)
        # 当窗口大小改变时，如果有当前帧，重新显示以适应新大小
        if hasattr(self, 'current_frame') and self.current_frame is not None:
            self.display_frame(self.current_frame)

    def on_stitch_mode_changed(self, mode):
        """当拼接模式改变时的处理"""
        self.save_settings()

    def update_stitch_progress(self, value, message):
        """更新拼接进度"""
        self.progress_bar.setValue(value)
        self.progress_bar.setFormat(message)

    def handle_stitch_result(self, success, message, data):
        """处理拼接结果"""
        if success:
            self.status_label.setText("拼接动画制作完成")
            self.status_label.setStyleSheet("color: green;")
        else:
            self.status_label.setText(f"拼接失败: {message}")
            self.status_label.setStyleSheet("color: red;")

        self.cleanup_animation_ui()

    def cleanup_animation_ui(self):
        """清理动画制作相关的UI状态"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)
        self.progress_bar.setValue(0)
        
        # 重新启用按钮
        self.make_gif_btn.setEnabled(True)
        self.add_position_btn.setEnabled(True)
        self.remove_position_btn.setEnabled(True)
        self.clear_positions_btn.setEnabled(True)
        self.cancel_btn.setEnabled(False)
        
        # 3秒后清除状态信息
        QTimer.singleShot(3000, lambda: self.status_label.clear())

    def show_preview_settings(self):
        """显示预览设置对话框"""
        if not self.decoder:
            QMessageBox.warning(self, "警告", "请先打开视频文件")
            return
            
        if not self.preview_dialog:
            self.preview_dialog = PreviewSettingsDialog(self)
        
        if self.preview_dialog.exec() == QDialog.DialogCode.Accepted:
            settings = self.preview_dialog.get_settings()
            # 保存设置到类变量
            PreviewSettingsDialog.last_settings = settings
            # 应用设置
            self.generate_preview(settings)

    def generate_preview(self, settings):
        """生成预览图"""
        try:
            import numpy as np
            
            if not self.decoder:
                return
                    
            # 获取视频尺寸（不需要读取第一帧）
            frame_height = int(self.decoder.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            frame_width = int(self.decoder.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            
            # 获取控制面板设置的输出尺寸
            try:
                output_width = self.width_input.value()
                output_height = self.height_input.value()
                
                # 检查尺寸模式
                size_mode = self.size_combo.currentText() if hasattr(self, 'size_combo') else "自定义尺寸"
                
                if size_mode == "原始尺寸":
                    # 使用视频原始尺寸的比例
                    aspect_ratio = frame_height / frame_width
                    cell_width = frame_width  # 使用原始宽度
                    cell_height = frame_height  # 使用原始高度
                else:
                    # 使用自定义尺寸的比例
                    cell_width = output_width  # 使用设置的宽度
                    cell_height = output_height  # 使用设置的高度
            except AttributeError:
                # 如果控件不存在，使用默认值
                aspect_ratio = frame_height / frame_width
                cell_width = frame_width  # 默认使用原始宽度
                cell_height = frame_height  # 默认使用原始高度
            
            # 计算总预览图尺寸
            total_width = cell_width * settings['cols'] + settings['h_spacing'] * (settings['cols'] - 1)
            total_height = cell_height * settings['rows'] + settings['v_spacing'] * (settings['rows'] - 1)
            
            # 创建预览图背景
            preview_img = np.zeros((
                total_height + settings['top_margin'] + settings['bottom_margin'],
                total_width + settings['left_margin'] + settings['right_margin'],
                3
            ), dtype=np.uint8)
            
            # 确保颜色设置存在
            if 'bg_color' not in settings or settings['bg_color'] is None:
                # 如果颜色不存在，调用默认设置方法
                self._setup_default_settings()
                # 使用设置后的颜色
                bg_color = settings.get('bg_color', PreviewSettingsDialog.last_settings['bg_color'])
            else:
                bg_color = settings['bg_color']
            
            # 设置背景
            if settings.get('use_bg_image') and (settings.get('bg_image_data') is not None or settings.get('bg_image_path')):
                try:
                    bg_img = None
                    
                    # 首先检查是否有内存中的图像数据
                    if settings.get('bg_image_data') is not None:
                        bg_img = settings['bg_image_data'].copy()
                    elif settings.get('bg_image_path'):
                        # 使用OpenCV读取图片
                        bg_img = cv2_imread_chinese(settings['bg_image_path'])
                        if bg_img is None:
                            raise Exception("无法读取背景图片")
                    
                    if bg_img is not None:
                        # 调整背景图大小
                        if settings.get('tile_bg'):
                            # 平铺背景
                            bg_h, bg_w = bg_img.shape[:2]
                            tile_img = np.zeros(preview_img.shape, dtype=np.uint8)
                            tiles_x = int(np.ceil(preview_img.shape[1] / bg_w))
                            tiles_y = int(np.ceil(preview_img.shape[0] / bg_h))
                            
                            for y in range(tiles_y):
                                for x in range(tiles_x):
                                    y_start = y * bg_h
                                    y_end = min((y + 1) * bg_h, preview_img.shape[0])
                                    x_start = x * bg_w
                                    x_end = min((x + 1) * bg_w, preview_img.shape[1])
                                    
                                    copy_h = y_end - y_start
                                    copy_w = x_end - x_start
                                    
                                    tile_img[y_start:y_end, x_start:x_end] = bg_img[:copy_h, :copy_w]
                            
                            bg_img = tile_img
                        else:
                            if settings.get('keep_ratio'):
                                # 保持原始比例
                                bg_h, bg_w = bg_img.shape[:2]
                                bg_ratio = bg_w / bg_h
                                
                                if preview_img.shape[1] / preview_img.shape[0] > bg_ratio:
                                    new_w = preview_img.shape[1]
                                    new_h = int(new_w / bg_ratio)
                                else:
                                    new_h = preview_img.shape[0]
                                    new_w = int(new_h * bg_ratio)
                                
                                bg_img = cv2.resize(bg_img, (new_w, new_h), interpolation=cv2.INTER_AREA)
                                
                                # 居中放置
                                y_offset = max(0, (preview_img.shape[0] - new_h) // 2)
                                x_offset = max(0, (preview_img.shape[1] - new_w) // 2)
                                
                                temp_bg = np.zeros(preview_img.shape, dtype=np.uint8)
                                copy_h = min(new_h, preview_img.shape[0] - y_offset)
                                copy_w = min(new_w, preview_img.shape[1] - x_offset)
                                
                                temp_bg[y_offset:y_offset+copy_h, x_offset:x_offset+copy_w] = bg_img[:copy_h, :copy_w]
                                bg_img = temp_bg
                            else:
                                bg_img = cv2.resize(bg_img, (preview_img.shape[1], preview_img.shape[0]), 
                                                  interpolation=cv2.INTER_AREA)
                        
                        # 应用透明度
                        opacity = settings.get('bg_opacity', 1.0)
                        # 创建纯色背景 (使用BGR顺序)
                        color_bg = np.zeros(preview_img.shape, dtype=np.uint8)
                        color_bg[:, :] = [bg_color.blue(), bg_color.green(), bg_color.red()] if bg_color else [240, 240, 240]
                        
                        # 混合背景图和纯色背景
                        preview_img = cv2.addWeighted(bg_img, opacity, color_bg, 1 - opacity, 0)
                except Exception as e:
                    print(f"加载背景图片失败: {str(e)}")
                    # 使用纯色背景作为备选
                    preview_img[:, :] = [bg_color.blue(), bg_color.green(), bg_color.red()] if bg_color else [240, 240, 240]
            else:
                # 使用纯色背景
                preview_img[:, :] = [bg_color.blue(), bg_color.green(), bg_color.red()] if bg_color else [240, 240, 240]
            
            # 初始化帧位置信息列表
            self.current_preview_frames = []
            
            # 计算帧间隔
            total_frames = self.total_frames
            cells = settings['rows'] * settings['cols']
            
            if total_frames <= cells:
                # 如果总帧数小于等于预览格数，则使用除首尾外的所有帧
                frame_indices = range(1, total_frames - 1)
            else:
                # 从2%处开始，到99%处结束
                start_frame = int(total_frames * 0.02)  # 从2%处开始
                end_frame = int(total_frames * 0.99)    # 到99%处结束
                usable_range = end_frame - start_frame
                
                # 计算均匀间隔
                interval = usable_range / cells
                # 生成顺序分布的帧索引
                frame_indices = [start_frame + int(i * interval) for i in range(cells)]
            
            # 生成预览图
            for idx, frame_index in enumerate(frame_indices):
                if frame_index >= total_frames:
                    continue
                
                # 计算行列位置
                i = idx // settings['cols']
                j = idx % settings['cols']
                
                # 计算当前缩略图位置
                x = settings['left_margin'] + j * (cell_width + settings['h_spacing'])
                y = settings['top_margin'] + i * (cell_height + settings['v_spacing'])
                
                try:
                    # 获取当前帧
                    frame = self.decoder.seek_frame(frame_index)
                    if frame is None:
                        continue
                    
                    # 调整帧大小
                    resized_frame = cv2.resize(frame, (cell_width, cell_height), interpolation=cv2.INTER_AREA)
                    
                    # 将帧放入预览图
                    preview_img[y:y+cell_height, x:x+cell_width] = resized_frame
                    
                    # 保存帧位置信息
                    frame_info = {
                        'index': frame_index,
                        'x': x,
                        'y': y,
                        'width': cell_width,
                        'height': cell_height
                    }
                    self.current_preview_frames.append(frame_info)
                    
                except Exception as e:
                    print(f"处理帧 {frame_index} 失败: {str(e)}")
                    continue
            
            # 保存当前预览图 - 不要转换颜色空间
            self.current_preview_img = preview_img.copy()
            
            # 启用保存按钮
            if hasattr(self, 'save_preview_btn'):
                self.save_preview_btn.setEnabled(True)
            
            # 先显示原始预览图
            self.display_preview_image(self.current_preview_img)
            
            # 如果需要显示视频信息，添加到预览图上
            if settings.get('show_video_info', True):
                # 创建带有视频信息的预览图副本
                info_img = self.add_video_info_to_preview(settings)
                if info_img is not None:
                    # 保存带信息的预览图，用于导出
                    self.display_preview_with_info = info_img
                    # 显示带有视频信息的预览图
                    self.display_preview_image(info_img)
            else:
                # 确保display_preview_with_info为None或被重置
                self.display_preview_with_info = None
            
            # 更新预览信息
            if hasattr(self, 'preview_info'):
                # 不再显示预览范围信息
                self.preview_info.setText("")
                # 下面是原来的代码，现在注释掉
                # start_time = self.frame_to_timestamp(0)
                # end_time = self.frame_to_timestamp(self.total_frames - 1)
                # self.preview_info.setText(f"预览范围: {start_time} - {end_time} (共{total_frames}帧)")
            
            # 在 generate_preview 方法的最后添加
            print(f"预览图生成完成，共有 {len(self.current_preview_frames)} 个帧位置信息")
            
            # 在方法结束前直接更新预览帧位置信息
            self.preview_frame_positions = self.current_preview_frames.copy()
            print(f"已直接更新 preview_frame_positions，现有 {len(self.preview_frame_positions)} 个帧位置信息")
            
            # 保存原始帧信息，用于后续替换帧的映射
            if not hasattr(self, 'original_frame_mapping'):
                self.original_frame_mapping = {}
            else:
                # 清空之前的映射，避免旧数据干扰
                self.original_frame_mapping.clear()
            
            # 更新原始帧映射
            for i, frame_info in enumerate(self.preview_frame_positions):
                # 使用位置索引作为键，而不是帧索引
                self.original_frame_mapping[i] = frame_info.copy()
                # 添加位置序号，用于后续应用替换帧
                frame_info['position_index'] = i
            
            # 应用已替换的帧
            self.apply_replaced_frames()
            
            # 启用保存按钮
            if hasattr(self, 'save_preview_btn'):
                self.save_preview_btn.setEnabled(True)
            # 添加这一行来启用保存所有单帧按钮
            if hasattr(self, 'save_all_frames_btn'):
                self.save_all_frames_btn.setEnabled(True)
            
            # 在生成预览图时启用恢复按钮
            self.restore_frames_btn.setEnabled(True)
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"生成预览图失败: {str(e)}")
            print(f"生成预览图失败: {str(e)}")
            traceback.print_exc()

    def connect_preview_signals(self):
        """连接预览图相关的信号"""
        # 尺寸设置变化
        if hasattr(self, 'size_combo'):
            self.size_combo.currentIndexChanged.connect(self.update_preview_delayed)
        if hasattr(self, 'width_input'):
            self.width_input.valueChanged.connect(self.update_preview_delayed)
        if hasattr(self, 'height_input'):
            self.height_input.valueChanged.connect(self.update_preview_delayed)
            
        # 为预览图标签添加鼠标跟踪
        if hasattr(self, 'preview_area'):
            self.preview_area.setMouseTracking(True)
            self.preview_area.mousePressEvent = self.preview_mouse_press_event

    def update_preview_delayed(self):
        """延迟更新预览图，避免频繁更新"""
        if not hasattr(self, 'preview_timer'):
            self.preview_timer = QTimer()
            self.preview_timer.setSingleShot(True)
            self.preview_timer.timeout.connect(self.update_preview_now)
        
        # 停止现有的计时器并重新启动
        self.preview_timer.stop()
        self.preview_timer.start(500)  # 500ms延迟
        
        # 更新状态提示
        if hasattr(self, 'status_label'):
            self.status_label.setText("预览图将在参数调整完成后更新...")
            self.status_label.setStyleSheet("color: blue;")

    def update_preview_now(self):
        """立即更新预览图"""
        if not self.decoder:
            return
            
        # 使用 PreviewSettingsDialog 的最后保存设置
        if not hasattr(PreviewSettingsDialog, 'last_settings'):
            return
            
        settings = PreviewSettingsDialog.last_settings.copy()
        
        # 如果有预览对话框，使用其设置（可能包含尚未保存到 last_settings 的更改）
        if hasattr(self, 'preview_dialog') and self.preview_dialog:
            try:
                dialog_settings = self.preview_dialog.get_settings()
                settings.update(dialog_settings)
            except:
                pass
            
        self.generate_preview(settings)
        
        # 更新状态提示
        if hasattr(self, 'status_label'):
            self.status_label.setText("预览图已更新")
            self.status_label.setStyleSheet("color: green;")
            QTimer.singleShot(2000, lambda: self.status_label.clear())
        
        # 确保预览帧位置信息被正确设置
        if not hasattr(self, 'preview_frame_positions'):
            self.preview_frame_positions = []
            
        # 直接从 current_preview_frames 复制，避免引用问题
        if hasattr(self, 'current_preview_frames') and self.current_preview_frames:
            self.preview_frame_positions = self.current_preview_frames.copy()
        else:
            print("警告: 没有找到 current_preview_frames")

    def save_preview_settings(self):
        """保存预览设置到文件"""
        try:
            if not hasattr(PreviewSettingsDialog, 'last_settings'):
                return
                
            # 将 QColor 对象转换为可序列化的字典
            settings = PreviewSettingsDialog.last_settings.copy()
            
            # 处理所有颜色对象
            for color_key in ['bg_color', 'info_bg_color', 'info_text_color']:
                if color_key in settings and settings[color_key] is not None:
                    settings[color_key] = {
                        'r': settings[color_key].red(),
                        'g': settings[color_key].green(),
                        'b': settings[color_key].blue(),
                        'a': settings[color_key].alpha()
                    }
            
            # 移除不可序列化的数据
            if 'bg_image_data' in settings:
                del settings['bg_image_data']

            # 移除字体对象（PIL.ImageFont.FreeTypeFont）
            for key in list(settings.keys()):
                if 'font' in key.lower() and not isinstance(settings[key], (str, int, float, bool, type(None), dict, list)):
                    del settings[key]
                
            # 添加视频标识信息
            if hasattr(self, 'video_path') and self.video_path:
                video_id = self.get_video_identifier(self.video_path)
                if video_id:
                    settings['video_identifier'] = video_id
                    
                    # 保存当前预览帧位置
                    if hasattr(self, 'preview_frame_positions'):
                        settings['frame_positions'] = self.preview_frame_positions
            
            # 保存到文件
            settings_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "preview_settings.json")
            with open(settings_path, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存预览设置失败: {str(e)}")

    def load_preview_settings(self):
        """加载预览设置"""
        try:
            # 确保 PreviewSettingsDialog 已经初始化
            if not hasattr(PreviewSettingsDialog, 'last_settings'):
                return
                
            settings_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "preview_settings.json")
            if os.path.exists(settings_path):
                try:
                    with open(settings_path, 'r', encoding='utf-8') as f:
                        settings = json.loads(f.read())
                    
                    # 检查当前视频是否匹配保存的设置
                    if hasattr(self, 'video_path') and self.video_path:
                        current_video_id = self.get_video_identifier(self.video_path)
                        saved_video_id = settings.get('video_identifier')
                        
                        if current_video_id and saved_video_id and current_video_id == saved_video_id:
                            # 恢复预览帧位置
                            if 'frame_positions' in settings:
                                self.preview_frame_positions = settings['frame_positions']
                
                    # 将字典转换回 QColor 对象
                    from PyQt6.QtGui import QColor
                    color_keys = ['bg_color', 'info_bg_color', 'info_text_color']
                    for color_key in color_keys:
                        if color_key in settings and settings[color_key] is not None:
                            color_dict = settings[color_key]
                            settings[color_key] = QColor(
                                color_dict['r'],
                                color_dict['g'],
                                color_dict['b'],
                                color_dict.get('a', 255)
                            )
                    
                    # 更新设置
                    for key, value in settings.items():
                        if key in PreviewSettingsDialog.last_settings:
                            PreviewSettingsDialog.last_settings[key] = value
                except json.JSONDecodeError:
                    # 文件损坏，使用默认设置
                    try:
                        backup_path = f"{settings_path}.bak"
                        shutil.copy2(settings_path, backup_path)
                        os.remove(settings_path)
                    except:
                        pass
                    self._setup_default_settings()
            else:
                self._setup_default_settings()
        except:
            self._setup_default_settings()
    
    def _setup_default_settings(self):
        """设置默认颜色和其他设置"""
        from PyQt6.QtGui import QColor
        PreviewSettingsDialog.last_settings['bg_color'] = QColor(235, 235, 235)  # 白色
        PreviewSettingsDialog.last_settings['info_bg_color'] = QColor(235, 235, 235)  # 黑色
        PreviewSettingsDialog.last_settings['info_text_color'] = QColor(235, 235, 235)  # 白色

    def preview_mouse_press_event(self, event):
        """处理预览图上的鼠标点击事件"""
        # 检查是否有预览帧位置信息
        if not hasattr(self, 'preview_frame_positions'):
            print("属性 preview_frame_positions 不存在")
            return
            
        if not self.preview_frame_positions:
            print("预览帧位置信息为空")
            return
            
        print(f"预览帧位置信息存在，共 {len(self.preview_frame_positions)} 个帧")
        
        # 获取点击位置
        pos = event.position() if hasattr(event, 'position') else event.pos()
        x, y = pos.x(), pos.y()
        
        # 计算预览图的缩放比例和偏移量
        preview_pixmap = self.preview_area.pixmap()
        if not preview_pixmap:
            print("没有预览图")
            return
        
        # 获取预览区域和预览图的尺寸
        preview_width = self.preview_area.width()
        preview_height = self.preview_area.height()
        pixmap_width = preview_pixmap.width()
        pixmap_height = preview_pixmap.height()
        
        print(f"预览区域尺寸: {preview_width}x{preview_height}")
        print(f"预览图尺寸: {pixmap_width}x{pixmap_height}")
        
        # 计算预览图在预览区域中的位置（居中显示）
        x_offset = (preview_width - pixmap_width) // 2
        y_offset = (preview_height - pixmap_height) // 2
        
        # 调整点击坐标，考虑偏移量
        x = x - x_offset
        y = y - y_offset
        
        # 检查点击是否在预览图内
        if x < 0 or y < 0 or x >= pixmap_width or y >= pixmap_height:
            print(f"点击在预览图外: ({x}, {y})")
            return
        
        # 计算缩放比例
        scale_x = pixmap_width / self.current_preview_img.shape[1]
        scale_y = pixmap_height / self.current_preview_img.shape[0]
        
        # 转换为原始预览图上的坐标
        original_x = int(x / scale_x)
        original_y = int(y / scale_y)
        
        print(f"点击坐标: 屏幕({pos.x()}, {pos.y()}), 预览图({x}, {y}), 原始图({original_x}, {original_y})")
        print(f"缩放比例: x={scale_x}, y={scale_y}")
        
        # 打印所有帧的位置信息，帮助调试
        print("所有帧位置信息:")
        for i, frame in enumerate(self.preview_frame_positions):
            print(f"  帧 {i}: 索引={frame['index']}, 位置=({frame['x']},{frame['y']}), 尺寸={frame['width']}x{frame['height']}")
        
        # 查找点击的是哪个帧
        clicked_frame = None
        for i, frame_info in enumerate(self.preview_frame_positions):
            if (frame_info['x'] <= original_x < frame_info['x'] + frame_info['width'] and
                frame_info['y'] <= original_y < frame_info['y'] + frame_info['height']):
                clicked_frame = frame_info
                print(f"找到点击的帧: 索引={i}, 帧号={frame_info['index']}")
                break
        
        if clicked_frame:
            # 左键点击：跳转到对应帧
            if event.button() == Qt.MouseButton.LeftButton:
                frame_index = clicked_frame['index']
                self.slider.setValue(frame_index)
                self.status_label.setText(f"已跳转到帧: {frame_index}")
                self.status_label.setStyleSheet("color: green;")
                QTimer.singleShot(2000, lambda: self.status_label.clear())
                
            # 右键点击：显示上下文菜单
            elif event.button() == Qt.MouseButton.RightButton:
                self.show_preview_context_menu(event, clicked_frame)
        else:
            print("未找到点击的帧")
    
    def show_preview_context_menu(self, event, frame_info):
        """显示预览图的右键菜单"""
        context_menu = QMenu(self)
        
        # 用当前视频帧替换此预览帧
        if self.current_frame is not None:
            replace_action = context_menu.addAction("用当前视频帧替换此预览帧")
            replace_action.triggered.connect(lambda: self.replace_preview_frame(frame_info))
        
        # 添加当前帧位置到列表
        add_position_action = context_menu.addAction("添加此帧位置到列表")
        add_position_action.triggered.connect(lambda: self.add_specific_position(frame_info['index']))
        
        # 显示菜单
        pos = event.globalPosition() if hasattr(event, 'globalPosition') else event.globalPos()
        context_menu.exec(pos.toPoint() if hasattr(pos, 'toPoint') else pos)
    
    def add_specific_position(self, frame_index):
        """添加指定帧位置到列表"""
        if not self.decoder:
            return
            
        # 检查是否已存在该位置
        for i in range(self.position_list.count()):
            existing_pos = self.position_list.item(i).data(Qt.ItemDataRole.UserRole)
            if abs(existing_pos - frame_index) < 5:  # 允许5帧的误差
                self.status_label.setText("该位置附近已存在标记点")
                self.status_label.setStyleSheet("color: orange;")
                QTimer.singleShot(2000, lambda: self.status_label.clear())
                return
        
        # 获取帧的时间戳
        timestamp = self.frame_to_timestamp(frame_index)
        
        # 创建新的列表项
        item_text = f"位置 {self.position_list.count() + 1}: {timestamp} (帧: {frame_index})"
        item = QListWidgetItem(item_text)
        item.setData(Qt.ItemDataRole.UserRole, frame_index)
        
        # 添加到列表
        self.position_list.addItem(item)
        self.position_list.update_item_numbers()  # 更新所有项的编号
        
        # 显示成功提示
        self.status_label.setText(f"已添加位置: {timestamp}")
        self.status_label.setStyleSheet("color: green;")
        QTimer.singleShot(2000, lambda: self.status_label.clear())
        
        # 将焦点设回主窗口，确保键盘左右键可以正常使用
        self.setFocus()
    
    def replace_preview_frame(self, frame_info):
        """用当前视频帧替换预览图中的指定帧"""
        if self.current_frame is None or not hasattr(self, 'current_preview_img'):
            print("没有当前帧或预览图")
            return
            
        try:
            # 获取当前视频帧 (已经是BGR格式)
            current_video_frame = self.current_frame.copy()
            
            # 不需要转换颜色空间，保持BGR格式
            
            # 调整大小以匹配预览图中的帧尺寸
            resized_frame = cv2.resize(current_video_frame, 
                                      (frame_info['width'], frame_info['height']),
                                      interpolation=cv2.INTER_AREA)
            
            # 替换预览图中的帧
            y_start = frame_info['y']
            y_end = y_start + frame_info['height']
            x_start = frame_info['x']
            x_end = x_start + frame_info['width']
            
            print(f"替换区域: x={x_start}-{x_end}, y={y_start}-{y_end}")
            print(f"预览图尺寸: {self.current_preview_img.shape}")
            print(f"替换帧尺寸: {resized_frame.shape}")
            
            # 确保替换区域在预览图范围内
            if (y_end <= self.current_preview_img.shape[0] and 
                x_end <= self.current_preview_img.shape[1]):
                # 保存替换前的帧索引
                original_frame_index = frame_info['index']
                
                # 将替换的帧保存到字典中 (保持BGR格式)
                if not hasattr(self, 'replaced_frames'):
                    self.replaced_frames = {}
                
                # 使用位置索引作为键，而不是原始帧索引
                position_index = frame_info.get('position_index', -1)
                if position_index == -1:
                    # 如果没有位置索引，尝试查找匹配的位置
                    for i, pos_info in enumerate(self.preview_frame_positions):
                        if pos_info['index'] == original_frame_index:
                            position_index = i
                            break
                
                # 确保使用整数帧索引
                current_frame_index = int(self.slider.value())
                
                self.replaced_frames[position_index] = {
                    'frame_data': resized_frame.copy(),
                    'current_frame_index': current_frame_index,
                    'original_frame_index': int(original_frame_index),
                    'position': {
                        'x': x_start,
                        'y': y_start,
                        'width': frame_info['width'],
                        'height': frame_info['height']
                    }
                }
                
                # 计算精确时间（秒）
                exact_seconds = current_frame_index / self.fps
                print(f"替换帧: 索引={current_frame_index}, 时间={exact_seconds:.3f}秒")
                
                # 替换预览图中的帧
                self.current_preview_img[y_start:y_end, x_start:x_end] = resized_frame
                
                # 获取当前预览设置
                settings = None
                if hasattr(self, 'preview_dialog') and self.preview_dialog:
                    settings = self.preview_dialog.get_settings()
                else:
                    # 如果没有预览对话框，使用最后保存的设置
                    settings = PreviewSettingsDialog.last_settings.copy()
                
                # 如果需要显示视频信息，重新添加
                if settings and settings.get('show_video_info', True):
                    # 重新生成带视频信息的预览图
                    info_img = self.add_video_info_to_preview(settings)
                    if info_img is not None:
                        # 更新带信息的预览图
                        self.display_preview_with_info = info_img
                        # 显示带有视频信息的预览图
                        self.display_preview_image(info_img)
                    else:
                        # 如果添加信息失败，显示不带信息的预览图
                        self.display_preview_image(self.current_preview_img)
                else:
                    # 显示不带视频信息的预览图
                    self.display_preview_image(self.current_preview_img)
                
                # 更新帧信息，确保使用整数帧索引
                frame_info['index'] = current_frame_index
                
                # 显示成功提示
                self.status_label.setText(f"已精确替换预览图中的帧 (原始帧: {original_frame_index}, 新帧: {current_frame_index})")
                self.status_label.setStyleSheet("color: green;")
                QTimer.singleShot(2000, lambda: self.status_label.clear())
                
                print(f"已精确保存替换帧信息: 位置索引 {position_index}, 原始帧 {original_frame_index} -> 当前帧 {current_frame_index}")
                
                # 保存替换帧记录
                self.save_replaced_frames()
                
            else:
                print("替换区域超出预览图范围")
                self.status_label.setText("替换失败：区域超出范围")
                self.status_label.setStyleSheet("color: red;")
                QTimer.singleShot(2000, lambda: self.status_label.clear())
            
        except Exception as e:
            print(f"替换帧时出错: {str(e)}")
            traceback.print_exc()
            self.status_label.setText(f"替换失败: {str(e)}")
            self.status_label.setStyleSheet("color: red;")
            QTimer.singleShot(2000, lambda: self.status_label.clear())

    def apply_replaced_frames(self):
        """应用已替换的帧到当前预览图"""
        if not hasattr(self, 'replaced_frames') or not self.replaced_frames:
            print("没有已替换的帧需要应用")
            return
        
        if not hasattr(self, 'current_preview_img') or self.current_preview_img is None:
            print("没有当前预览图")
            return
        
        try:
            print(f"应用 {len(self.replaced_frames)} 个已替换的帧...")
            
            # 获取当前预览设置
            settings = None
            if hasattr(self, 'preview_dialog') and self.preview_dialog:
                settings = self.preview_dialog.get_settings()
            else:
                settings = PreviewSettingsDialog.last_settings.copy()
            
            # 遍历所有替换的帧
            for position_index, replacement in self.replaced_frames.items():
                # 检查位置索引是否有效
                if position_index < 0 or position_index >= len(self.preview_frame_positions):
                    print(f"位置索引 {position_index} 超出范围，跳过")
                    continue
                
                # 获取当前位置信息
                new_pos = self.preview_frame_positions[position_index]
                
                # 从原始视频中重新获取该帧，使用精确帧定位
                original_frame_index = replacement['current_frame_index']
                print(f"尝试精确定位帧 {original_frame_index}")
                frame = self.decoder.seek_frame_precise(original_frame_index)
                
                if frame is not None:
                    # 调整帧大小以匹配当前位置信息
                    replaced_frame = cv2.resize(frame, 
                                        (new_pos['width'], new_pos['height']),
                                        interpolation=cv2.INTER_AREA)
                    
                    # 更新替换帧数据，保存调整后的帧
                    replacement['frame_data'] = replaced_frame.copy()
                    
                    # 替换预览图中的帧
                    y_start = new_pos['y']
                    y_end = y_start + new_pos['height']
                    x_start = new_pos['x']
                    x_end = x_start + new_pos['width']
                    
                    # 确保替换区域在预览图范围内
                    if (y_end <= self.current_preview_img.shape[0] and 
                        x_end <= self.current_preview_img.shape[1]):
                        # 更新预览图
                        self.current_preview_img[y_start:y_end, x_start:x_end] = replaced_frame
                        
                        # 更新位置信息
                        replacement['position'] = {
                            'x': x_start,
                            'y': y_start,
                            'width': new_pos['width'],
                            'height': new_pos['height']
                        }
                        
                        # 更新帧索引信息
                        new_pos['index'] = replacement['current_frame_index']
                        
                        print(f"已应用替换帧: 位置索引 {position_index}, 位置 ({x_start},{y_start})")
                    else:
                        print(f"替换区域超出预览图范围: 位置索引 {position_index}, 位置 ({x_start},{y_start}), 预览图尺寸 {self.current_preview_img.shape}")
            
            # 获取当前预览设置
            settings = None
            if hasattr(self, 'preview_dialog') and self.preview_dialog:
                settings = self.preview_dialog.get_settings()
            else:
                # 如果没有预览对话框，使用最后保存的设置
                settings = PreviewSettingsDialog.last_settings.copy()
            
            # 如果需要显示视频信息，重新添加
            if settings and settings.get('show_video_info', True):
                # 重新生成带视频信息的预览图
                info_img = self.add_video_info_to_preview(settings)
                if info_img is not None:
                    # 更新带信息的预览图
                    self.display_preview_with_info = info_img
                    # 显示带有视频信息的预览图
                    self.display_preview_image(info_img)
                    return
            
            # 如果没有显示视频信息或出现错误，直接显示原始预览图
            self.display_preview_image(self.current_preview_img)
            
        except Exception as e:
            print(f"应用替换帧时出错: {str(e)}")
            traceback.print_exc()


    def generate_group_preview(self, group, settings):
        """生成组预览图"""
        try:
            if not group:
                return None
                
            # 从当前预览图中获取实际的单元格尺寸
            preview_h, preview_w = self.current_preview_img.shape[:2]
            cell_width = (preview_w - settings['left_margin'] - settings['right_margin'] - 
                         (settings['cols'] - 1) * settings['h_spacing']) // settings['cols']
            cell_height = (preview_h - settings['top_margin'] - settings['bottom_margin'] - 
                          (settings['rows'] - 1) * settings['v_spacing']) // settings['rows']
            
            # 计算新画布大小
            new_width = (cell_width * settings['export_cols'] + 
                        settings['h_spacing'] * (settings['export_cols'] - 1) +
                        settings['left_margin'] + settings['right_margin'])
            new_height = (cell_height * settings['export_rows'] + 
                         settings['v_spacing'] * (settings['export_rows'] - 1) +
                         settings['top_margin'] + settings['bottom_margin'])
            
            # 创建新画布
            new_preview = np.zeros((new_height, new_width, 3), dtype=np.uint8)
            
            # 应用背景
            if settings.get('use_bg_image'):
                bg_img = None
                
                # 首先检查是否有内存中的图像数据
                if settings.get('bg_image_data') is not None:
                    bg_img = settings['bg_image_data'].copy()
                    print("使用内存中的图像数据作为背景")
                elif settings.get('bg_image_path'):
                    # 使用OpenCV读取图片
                    bg_img = cv2_imread_chinese(settings['bg_image_path'])
                    if bg_img is None:
                        raise Exception("无法读取背景图片")
                    print(f"使用OpenCV读取背景图: {settings['bg_image_path']}")
                
                if bg_img is not None:
                    # 调整背景图大小
                    if settings.get('tile_bg'):
                        # 平铺背景
                        bg_h, bg_w = bg_img.shape[:2]
                        tile_img = np.zeros(new_preview.shape, dtype=np.uint8)
                        tiles_x = int(np.ceil(new_preview.shape[1] / bg_w))
                        tiles_y = int(np.ceil(new_preview.shape[0] / bg_h))
                        
                        for y in range(tiles_y):
                            for x in range(tiles_x):
                                y_start = y * bg_h
                                y_end = min((y + 1) * bg_h, new_preview.shape[0])
                                x_start = x * bg_w
                                x_end = min((x + 1) * bg_w, new_preview.shape[1])
                                
                                copy_h = y_end - y_start
                                copy_w = x_end - x_start
                                
                                tile_img[y_start:y_end, x_start:x_end] = bg_img[:copy_h, :copy_w]
                        
                        bg_img = tile_img
                    else:
                        # 调整背景图大小以适应预览图
                        if settings.get('keep_ratio'):
                            # 保持原始比例
                            bg_h, bg_w = bg_img.shape[:2]
                            bg_ratio = bg_w / bg_h
                            
                            if new_preview.shape[1] / new_preview.shape[0] > bg_ratio:
                                new_w = new_preview.shape[1]
                                new_h = int(new_w / bg_ratio)
                            else:
                                new_h = new_preview.shape[0]
                                new_w = int(new_h * bg_ratio)
                            
                            bg_img = cv2.resize(bg_img, (new_w, new_h), interpolation=cv2.INTER_AREA)
                            
                            # 居中放置
                            y_offset = max(0, (new_preview.shape[0] - new_h) // 2)
                            x_offset = max(0, (new_preview.shape[1] - new_w) // 2)
                            
                            temp_bg = np.zeros(new_preview.shape, dtype=np.uint8)
                            copy_h = min(new_h, new_preview.shape[0] - y_offset)
                            copy_w = min(new_w, new_preview.shape[1] - x_offset)
                            
                            temp_bg[y_offset:y_offset+copy_h, x_offset:x_offset+copy_w] = bg_img[:copy_h, :copy_w]
                            bg_img = temp_bg
                        else:
                            bg_img = cv2.resize(bg_img, (new_preview.shape[1], new_preview.shape[0]), 
                                              interpolation=cv2.INTER_AREA)
                    
                    # 应用透明度
                    opacity = settings.get('bg_opacity', 1.0)
                    if 'bg_color' in settings:
                        bg_color = settings['bg_color']
                        color_bg = np.ones(new_preview.shape, dtype=np.uint8)
                        color_bg[:, :, 0] = bg_color.blue()
                        color_bg[:, :, 1] = bg_color.green()
                        color_bg[:, :, 2] = bg_color.red()
                    else:
                        color_bg = np.ones(new_preview.shape, dtype=np.uint8) * 255
                    
                    new_preview = cv2.addWeighted(bg_img, opacity, color_bg, 1 - opacity, 0)
            else:
                # 使用纯色背景
                bg_color = settings.get('bg_color')  # 直接从设置中获取背景颜色
                if bg_color:
                    new_preview[:, :, 0] = bg_color.blue()
                    new_preview[:, :, 1] = bg_color.green()
                    new_preview[:, :, 2] = bg_color.red()
                else:
                    new_preview.fill(255)  # 默认白色背景
            
            # 放置每个帧
            for idx, frame_info in enumerate(group):
                if idx >= settings['export_rows'] * settings['export_cols']:
                    break
                    
                row = idx // settings['export_cols']
                col = idx % settings['export_cols']
                
                # 计算新位置
                x = settings['left_margin'] + col * (cell_width + settings['h_spacing'])
                y = settings['top_margin'] + row * (cell_height + settings['v_spacing'])
                
                # 从原始预览图中复制帧
                frame_region = self.current_preview_img[
                    frame_info['y']:frame_info['y'] + frame_info['height'],
                    frame_info['x']:frame_info['x'] + frame_info['width']
                ]
                
                # 放置到新位置
                try:
                    new_preview[y:y+cell_height, x:x+cell_width] = frame_region
                except ValueError as e:
                    print(f"放置帧失败: {str(e)}")
                    continue
            
            return new_preview
            
        except Exception as e:
            print(f"生成组预览图时出错: {str(e)}")
            traceback.print_exc()
            return None

    def save_all_preview_frames(self):
        """保存预览图中的所有单帧图像到控制面板设置的路径"""
        if not hasattr(self, 'preview_frame_positions') or not self.preview_frame_positions:
            QMessageBox.warning(self, "警告", "没有可用的预览帧")
            return
            
        if not hasattr(self, 'current_preview_img') or self.current_preview_img is None:
            QMessageBox.warning(self, "警告", "没有可用的预览图")
            return
            
        try:
            # 获取保存路径
            save_dir = self.path_input.text()
            
            # 如果选择了保存到视频目录且有视频路径
            if self.save_to_video_dir_checkbox.isChecked() and hasattr(self, 'video_path') and self.video_path:
                save_dir = os.path.dirname(os.path.abspath(self.video_path))
                
            if not save_dir or not os.path.exists(save_dir):
                QMessageBox.warning(self, "警告", f"保存路径无效或不存在: {save_dir}")
                return
                
            # 获取格式设置
            format_type = self.save_format_combo.currentText().lower()
            
            # 获取视频文件名（不含扩展名）
            if self.video_path:
                video_name = os.path.splitext(os.path.basename(self.video_path))[0]
            else:
                video_name = "preview"
            
            # 创建进度对话框
            progress = QProgressDialog("正在保存单帧图像...", "取消", 0, len(self.preview_frame_positions), self)
            progress.setWindowTitle("保存进度")
            progress.setWindowModality(Qt.WindowModality.WindowModal)
            progress.setMinimumDuration(0)
            
            # 创建子文件夹用于保存单帧图像
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            frames_dir = os.path.join(save_dir, f"{video_name}_frames_{timestamp}")
            os.makedirs(frames_dir, exist_ok=True)
            
            # 保存每一帧
            saved_count = 0
            
            # 获取ffmpeg路径
            try:
                ffmpeg_path = get_ffmpeg_path()
            except Exception as e:
                QMessageBox.warning(self, "错误", f"无法获取FFMPEG路径: {str(e)}")
                return
            
            # 检查视频路径是否可用
            if not hasattr(self, 'video_path') or not self.video_path or not os.path.exists(self.video_path):
                QMessageBox.warning(self, "错误", "无法找到源视频文件")
                return
            
            # 获取主界面的尺寸设置
            if self.size_combo.currentText() == "自定义尺寸":
                output_width = self.width_input.value()
                output_height = self.height_input.value()
            else:
                # 使用原始尺寸
                output_width = 0
                output_height = 0
            
            # 对预览帧位置按帧索引排序，确保按顺序保存
            sorted_frames = self.preview_frame_positions.copy()
            
            for i, frame_info in enumerate(sorted_frames):
                # 更新进度
                progress.setValue(i)
                if progress.wasCanceled():
                    break
                
                # 获取帧索引和帧时间
                frame_index = frame_info['index']
                original_frame_index = frame_index
                
                # 检查是否是替换帧，并获取正确的帧索引
                position_index = i
                if hasattr(self, 'replaced_frames') and position_index in self.replaced_frames:
                    replacement = self.replaced_frames.get(position_index)
                    if replacement and 'current_frame_index' in replacement:
                        frame_index = replacement['current_frame_index']
                        print(f"使用替换帧: 位置索引 {position_index}, 原始帧 {original_frame_index} -> 当前帧 {frame_index}")
                
                # 计算帧对应的时间点（秒）
                frame_time = frame_index / self.fps if self.fps > 0 else 0
                
                # 构建文件名
                file_name = f"{i+1:03d}_{video_name}_frame_{frame_index:06d}.{format_type}"
                file_path = os.path.join(frames_dir, file_name)
                
                try:
                    # 构建基本FFMPEG命令
                    cmd = [
                        ffmpeg_path,
                        "-ss", str(frame_time),
                        "-i", self.video_path,
                        "-frames:v", "1"
                    ]
                    
                    # 添加尺寸调整参数
                    if output_width > 0 and output_height > 0:
                        cmd.extend(["-s", f"{output_width}x{output_height}"])
                    
                    # 根据格式添加特定参数
                    if format_type == 'png':
                        cmd.extend([
                            "-pix_fmt", "rgba",
                            "-compression_level", "0"
                        ])
                    else:  # jpg
                        cmd.extend([
                            "-q:v", "1",
                            "-qscale:v", "1",
                            "-compression_level", "0",
                            "-pix_fmt", "yuvj444p"
                        ])
                    
                    # 添加输出路径
                    cmd.append("-y")
                    cmd.append(file_path)
                    
                    # 执行命令
                    print(f"执行FFmpeg {format_type.upper()}直接截取命令: {' '.join(cmd)}")
                    process = subprocess.run(
                        cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=False,
                        creationflags=subprocess.CREATE_NO_WINDOW
                    )
                    
                    if process.returncode == 0 and os.path.exists(file_path):
                        saved_count += 1
                        print(f"使用FFmpeg成功直接截取{format_type.upper()}帧 #{frame_index} (序号: {i+1})")
                    else:
                        print(f"FFmpeg直接截取帧失败: {process.stderr.decode('utf-8', errors='ignore')}")
                        
                except Exception as e:
                    print(f"保存帧 {frame_index} 时出错: {str(e)}")
                    continue
            
            # 完成进度
            progress.setValue(len(self.preview_frame_positions))
            
            # 显示结果
            if saved_count > 0:
                self.status_label.setText(f"已按顺序保存 {saved_count} 个单帧图像到: {frames_dir}")
                self.status_label.setStyleSheet("color: green;")
                QTimer.singleShot(5000, lambda: self.status_label.clear())
                
                # 打开保存目录
                try:
                    if os.path.exists(frames_dir):
                        if sys.platform == 'win32':
                            os.startfile(frames_dir)
                        elif sys.platform == 'darwin':
                            subprocess.call(['open', frames_dir])
                        else:
                            subprocess.call(['xdg-open', frames_dir])
                except Exception as e:
                    print(f"打开保存目录失败: {str(e)}")
            else:
                self.status_label.setText("没有保存任何图像")
                self.status_label.setStyleSheet("color: red;")
                QTimer.singleShot(3000, lambda: self.status_label.clear())
            
        except Exception as e:
            self.status_label.setText(f"保存单帧图像时出错: {str(e)}")
            self.status_label.setStyleSheet("color: red;")
            QTimer.singleShot(3000, lambda: self.status_label.clear())
            print(f"保存单帧图像时出错: {str(e)}")
            traceback.print_exc()

    def update_format_ui(self):
        """根据选择的格式更新界面状态"""
        current_format = self.save_format_combo.currentText().lower()
        
        # 检查jpg_quality_spin是否存在
        if hasattr(self, 'jpg_quality_spin'):
            # 如果是JPG格式，显示质量设置
            if current_format == 'jpg':
                self.jpg_quality_spin.setVisible(True)
                # 查找质量标签
                for label in self.findChildren(QLabel):
                    if label.text() == "质量:":
                        label.setVisible(True)
                        break
            else:
                # 如果是PNG格式，隐藏质量设置
                self.jpg_quality_spin.setVisible(False)
                # 查找质量标签
                for label in self.findChildren(QLabel):
                    if label.text() == "质量:":
                        label.setVisible(False)
                        break

    def test_jpg_quality(self):
        """测试不同质量设置的JPG保存效果"""
        try:
            if not hasattr(self, 'current_preview_img'):
                QMessageBox.warning(self, "错误", "请先生成预览图")
                return
                
            # 获取控制面板设置的保存路径
            save_dir = self.path_input.text()
            if not save_dir or not os.path.isdir(save_dir):
                QMessageBox.warning(self, "错误", "请先在控制面板设置有效的保存路径")
                return
            
            # 创建测试文件夹
            test_dir = os.path.join(save_dir, "jpg_quality_test")
            os.makedirs(test_dir, exist_ok=True)
            
            # 生成基础文件名
            video_name = Path(self.video_path).stem if self.video_path else "preview"
            video_name = re.sub(r'[\\/*?:"<>|]', "_", video_name)
            
            # 使用PIL保存不同质量的JPG
            from PIL import Image
            img = Image.fromarray(cv2.cvtColor(self.current_preview_img, cv2.COLOR_BGR2RGB))
            
            # 测试不同质量设置
            for quality in [10, 30, 50, 70, 90, 95, 100]:
                file_path = os.path.join(test_dir, f"{video_name}_quality_{quality}.jpg")
                
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                img.save(file_path, 
                        format='JPEG',
                        quality=quality,
                        optimize=True,
                        progressive=True,
                        subsampling=0
                )
                print(f"已保存质量为 {quality} 的JPG图像，大小: {os.path.getsize(file_path)} 字节")
            
            # 打开测试文件夹
            try:
                if sys.platform == 'win32':
                    os.startfile(test_dir)
                elif sys.platform == 'darwin':  # macOS
                    subprocess.call(['open', test_dir])
                else:  # Linux
                    subprocess.call(['xdg-open', test_dir])
            except Exception as e:
                print(f"打开测试文件夹失败: {str(e)}")
                
            QMessageBox.information(self, "成功", f"已生成不同质量的JPG测试图像，保存在:\n{test_dir}")
                
        except Exception as e:
            print(f"测试JPG质量时出错: {str(e)}")
            traceback.print_exc()
            QMessageBox.warning(self, "错误", f"测试JPG质量失败: {str(e)}")

    
    def add_video_info_to_preview(self, settings):
        """在预览图信息区添加视频信息 - iOS风格渐变灰色设计"""
        if self.current_preview_img is None or not self.decoder:
            return None

        try:
            import numpy as np
            import subprocess
            import json
            import os
            import traceback
            import cv2
            import math
            import sys
            from PyQt6.QtWidgets import QApplication
            from PyQt6.QtGui import (
                QFont, QColor, QImage, QFontMetrics, QPixmap, QPainter,
                QPen, QLinearGradient, QPainterPath, QBrush, QRadialGradient
            )
            from PyQt6.QtCore import Qt, QRect, QSize, QPoint, QRectF

            # 创建一个副本，避免修改原始预览图
            display_img = self.current_preview_img.copy()

            # 准备默认值
            video_name = os.path.basename(self.video_path) if self.video_path else "未知文件"
            video_resolution = "未知"
            video_duration = "未知"
            video_fps = f"{self.fps:.2f} fps"  # 默认值
            file_size = "未知"

            # 获取字体大小 - 使用与enhanced_font_manager.py完全一致的设置逻辑
            font_size = settings.get('info_font_size', 16)  # 字体大小，默认三号字

            # 使用中文字号映射表，与enhanced_font_manager.py完全相同
            size_map = {
                "初号": 42.0,
                "小初": 36.0,
                "一号": 26.0,
                "小一": 24.0,
                "二号": 22.0,
                "小二": 18.0,
                "三号": 16.0,
                "四号": 14.0,
                "小四": 12.0,
                "五号": 10.5,
                "小五": 9.0,
                "六号": 7.5,
                "小六": 6.5,
                "七号": 5.5,
                "八号": 5.0
            }

            # 如果是字符串，可能是中文字号
            if isinstance(font_size, str) and font_size in size_map:
                font_size = size_map.get(font_size, 16.0)
            else:
                # 确保转换为浮点数并在合理范围内
                try:
                    font_size = float(font_size)
                except (ValueError, TypeError):
                    font_size = 16.0  # 默认三号字

                # 限制在合理范围内
                min_font_size = 5.0
                max_font_size = 42.0
                font_size = max(min_font_size, min(font_size, max_font_size))

            # 视频信息获取部分 - 保持原有逻辑
            try:
                # 检查同目录下的ffprobe.exe
                current_dir = os.path.dirname(os.path.abspath(__file__))
                ffprobe_path = os.path.join(current_dir, 'ffprobe.exe')

                if not os.path.exists(ffprobe_path):
                    raise FileNotFoundError("ffprobe.exe not found")

                ffprobe_cmd = [
                    ffprobe_path,
                    '-v', 'quiet',
                    '-print_format', 'json',
                    '-show_format',
                    '-show_streams',
                    self.video_path
                ]

                # 创建 startupinfo 对象来隐藏窗口
                startupinfo = None
                if sys.platform == "win32":
                    startupinfo = subprocess.STARTUPINFO()
                    startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                    startupinfo.wShowWindow = subprocess.SW_HIDE

                process = subprocess.Popen(
                    ffprobe_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    startupinfo=startupinfo
                )
                stdout, stderr = process.communicate()

                if process.returncode == 0 and stdout:
                    probe_data = json.loads(stdout.decode('utf-8', errors='ignore'))

                    # 获取格式信息
                    if 'format' in probe_data:
                        format_data = probe_data['format']

                        # 获取文件大小
                        if 'size' in format_data:
                            size_bytes = int(format_data['size'])
                            if size_bytes < 0:  # 处理负数情况
                                size_bytes = abs(size_bytes)  # 取绝对值

                            if size_bytes < 1024:
                                file_size = f"{size_bytes} 字节"
                            elif size_bytes < 1024 * 1024:
                                file_size = f"{size_bytes / 1024:.2f} KB"
                            elif size_bytes < 1024 * 1024 * 1024:
                                file_size = f"{size_bytes / (1024 * 1024):.2f} MB"
                            else:
                                file_size = f"{size_bytes / (1024 * 1024 * 1024):.2f} GB"

                        # 获取时长
                        if 'duration' in format_data:
                            duration = float(format_data['duration'])
                            hours = int(duration // 3600)
                            minutes = int((duration % 3600) // 60)
                            seconds = int(duration % 60)
                            video_duration = f"{hours:02d}:{minutes:02d}:{seconds:02d}"

                    # 获取视频流信息
                    if 'streams' in probe_data:
                        for stream in probe_data['streams']:
                            if stream.get('codec_type') == 'video':
                                # 获取分辨率
                                if 'width' in stream and 'height' in stream:
                                    width = stream['width']
                                    height = stream['height']
                                    video_resolution = f"{width}x{height}"

                                # 获取帧率
                                if 'avg_frame_rate' in stream:
                                    try:
                                        num, den = map(int, stream['avg_frame_rate'].split('/'))
                                        if den != 0:
                                            fps = num / den
                                            video_fps = f"{fps:.2f} fps"
                                    except:
                                        # 如果平均帧率获取失败，尝试获取实际帧率
                                        if 'r_frame_rate' in stream:
                                            try:
                                                num, den = map(int, stream['r_frame_rate'].split('/'))
                                                if den != 0:
                                                    fps = num / den
                                                    video_fps = f"{fps:.2f} fps"
                                            except:
                                                pass

            except FileNotFoundError:
                # 如果找不到ffprobe，使用系统方法获取信息
                if self.video_path and os.path.exists(self.video_path):
                    try:
                        size_bytes = os.path.getsize(self.video_path)
                        if size_bytes < 0:  # 处理负数情况
                            size_bytes = abs(size_bytes)  # 取绝对值

                        if size_bytes < 1024:
                            file_size = f"{size_bytes} 字节"
                        elif size_bytes < 1024 * 1024:
                            file_size = f"{size_bytes / 1024:.2f} KB"
                        elif size_bytes < 1024 * 1024 * 1024:
                            file_size = f"{size_bytes / (1024 * 1024):.2f} MB"
                        else:
                            file_size = f"{size_bytes / (1024 * 1024 * 1024):.2f} GB"
                    except Exception as e:
                        print(f"获取文件大小时出错: {str(e)}")

                # 使用OpenCV获取分辨率
                if hasattr(self, 'cap') and self.cap and self.cap.isOpened():
                    width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                    video_resolution = f"{width}x{height}"
                else:
                    first_frame = self.decoder.seek_frame(0)
                    if first_frame is not None:
                        height, width = first_frame.shape[:2]
                        video_resolution = f"{width}x{height}"

                # 使用帧数计算时长
                total_seconds = int(self.total_frames / self.fps)
                hours = total_seconds // 3600
                minutes = (total_seconds % 3600) // 60
                seconds = total_seconds % 60
                video_duration = f"{hours:02d}:{minutes:02d}:{seconds:02d}"

            except Exception as e:
                print(f"获取视频信息失败: {str(e)}")

            # 准备信息数据
            file_info = {
                "filename": video_name,  # 直接使用文件名，不添加前缀
                "resolution": video_resolution,
                "frame_rate": video_fps,
                "duration": video_duration,
                "file_size": file_size
            }
            
            # 获取原始预览图尺寸
            img_height, img_width = display_img.shape[:2]

            # 使用Qt字体度量来计算文本高度 - iOS风格字体设置
            qt_font = QFont()

            # 设置iOS风格字体 - 优先使用系统字体
            if settings.get('font_family'):
                qt_font.setFamily(settings.get('font_family'))
            else:
                # iOS风格字体优先级：SF Pro Display > Helvetica Neue > Segoe UI > Arial
                ios_fonts = ["SF Pro Display", "SF Pro", "Helvetica Neue", "Segoe UI", "Arial", "Microsoft YaHei UI"]
                for font in ios_fonts:
                    qt_font.setFamily(font)
                    if qt_font.exactMatch():
                        break

            # 设置字体大小
            qt_font.setPointSizeF(font_size)

            # iOS风格字体渲染设置
            qt_font.setStyleStrategy(QFont.StyleStrategy.PreferAntialias)
            qt_font.setHintingPreference(QFont.HintingPreference.PreferFullHinting)

            # iOS风格字重设置 - 通常使用Medium或Regular
            font_style = settings.get('font_style', '常规')
            if font_style == "粗体":
                qt_font.setWeight(QFont.Weight.Medium)  # iOS风格Medium字重
                qt_font.setItalic(False)
            elif font_style == "斜体":
                qt_font.setWeight(QFont.Weight.Normal)
                qt_font.setItalic(True)
            elif font_style == "粗斜体":
                qt_font.setWeight(QFont.Weight.Medium)
                qt_font.setItalic(True)
            else:  # 常规
                qt_font.setWeight(QFont.Weight.Normal)
                qt_font.setItalic(False)

            # 设置下划线和删除线
            qt_font.setUnderline(settings.get('font_underline', False))
            qt_font.setStrikeOut(settings.get('font_strikeout', False))

            # 获取字体度量
            font_metrics = QFontMetrics(qt_font)
            actual_height = font_metrics.height()

            # 保持原有布局参数，不增加高度
            left_margin = int(font_size * 1.0)  # 保持原有左边距
            line_height = int(actual_height * 1.1)  # 保持原有行高
            padding_v = int(actual_height * 0.5)  # 保持原有垂直内边距
            line_spacing = int(actual_height * 0.4)  # 保持原有行间距

            # 计算信息条高度 - 保持原有计算方式
            info_bar_height = line_height * 4 + padding_v * 2 + line_spacing * 3

            # 确定信息条位置
            info_position = settings.get('info_position', 'top')

            # 直接在原图像上绘制，不增加高度
            # 在预览图上方或下方的指定区域绘制信息
            if info_position == 'top':
                # 在顶部绘制信息，覆盖预览图的顶部区域
                info_y_start = 0
                info_area = display_img[0:info_bar_height, :]
            else:
                # 在底部绘制信息，覆盖预览图的底部区域
                info_y_start = img_height - info_bar_height
                info_area = display_img[info_y_start:img_height, :]

            # iOS风格颜色设置 - 使用渐变灰色主色调
            info_bg_color = settings.get('info_bg_color')
            if not info_bg_color or not hasattr(info_bg_color, 'red'):
                # iOS风格默认背景 - 浅灰色系
                info_bg_color = QColor(242, 242, 247)  # iOS系统浅灰色

            info_text_color = settings.get('info_text_color')
            if not info_text_color or not hasattr(info_text_color, 'red'):
                # iOS风格文本颜色 - 深灰色而非纯黑
                info_text_color = QColor(60, 60, 67)  # iOS系统深灰色

            # 为OpenCV图像创建BGR格式的颜色（备用，当前iOS风格设计中未使用）
            # opencv_text_color = QColor(info_text_color.blue(), info_text_color.green(), info_text_color.red())
            # opencv_bg_color = QColor(info_bg_color.blue(), info_bg_color.green(), info_bg_color.red())

            # 保持原有文本内容格式
            info_texts = [
                file_info["filename"],  # 第一行 - 文件名
                f"分辨率：{video_resolution} / {file_info['frame_rate']}",  # 第二行 - 分辨率和帧率
                f"视频时长：{video_duration}",  # 第三行 - 视频时长
                f"文件大小：{file_size}"  # 第四行 - 文件大小
            ]

            # 创建iOS风格的信息区域QPixmap
            info_pixmap = QPixmap(img_width, info_bar_height)

            # 初始化绘制器
            renderer = QPainter(info_pixmap)
            renderer.setRenderHint(QPainter.RenderHint.Antialiasing, True)
            renderer.setRenderHint(QPainter.RenderHint.TextAntialiasing, True)
            renderer.setRenderHint(QPainter.RenderHint.SmoothPixmapTransform, True)

            # iOS风格渐变灰色背景设计 - 半透明毛玻璃效果
            bg_rect = QRectF(0, 0, img_width, info_bar_height)

            # 创建iOS风格的垂直渐变 - 半透明毛玻璃效果
            ios_gradient = QLinearGradient(0, 0, 0, info_bar_height)

            # iOS风格的现代渐变 - 清新配色方案
            # 顶部：清新薄荷色
            top_color = QColor(230, 245, 245, 240)     # 清新薄荷色
            # 中部：柔和蓝绿色
            mid_color = QColor(220, 240, 242, 250)     # 柔和蓝绿色
            # 底部：淡雅青色
            bottom_color = QColor(210, 235, 240, 255)  # 淡雅青色

            # 设置渐变色阶
            ios_gradient.setColorAt(0.0, top_color)
            ios_gradient.setColorAt(0.5, mid_color)
            ios_gradient.setColorAt(1.0, bottom_color)

            # 绘制半透明渐变背景
            renderer.fillRect(bg_rect, QBrush(ios_gradient))

            # 添加iOS风格的清新边框
            border_color = QColor(174, 214, 220, 150)  # 清新色调边框
            renderer.setPen(QPen(border_color, 1.0))

            # 绘制边框
            if info_position == 'top':
                # 顶部信息条的底部边框
                renderer.drawLine(0, info_bar_height - 1, img_width, info_bar_height - 1)
            else:
                # 底部信息条的顶部边框
                renderer.drawLine(0, 0, img_width, 0)

            # 添加iOS风格的微妙内阴影效果
            inner_shadow_gradient = QLinearGradient(0, 0, 0, 8)
            if info_position == 'top':
                # 顶部信息条的内阴影
                inner_shadow_gradient.setColorAt(0.0, QColor(0, 0, 0, 30))
                inner_shadow_gradient.setColorAt(1.0, QColor(0, 0, 0, 0))
                shadow_rect = QRectF(0, 0, img_width, 8)
            else:
                # 底部信息条的内阴影
                inner_shadow_gradient.setColorAt(0.0, QColor(0, 0, 0, 0))
                inner_shadow_gradient.setColorAt(1.0, QColor(0, 0, 0, 30))
                shadow_rect = QRectF(0, info_bar_height - 8, img_width, 8)

            renderer.fillRect(shadow_rect, QBrush(inner_shadow_gradient))
            
            # 计算各行文本的Y位置
            y_positions = [
                padding_v,                               # 第一行
                padding_v + line_height + line_spacing,  # 第二行
                padding_v + 2 * line_height + 2 * line_spacing,  # 第三行
                padding_v + 3 * line_height + 3 * line_spacing  # 第四行
            ]

            # iOS风格的文本颜色 - 在清新背景上清晰可见
            text_colors = [
                QColor(44, 62, 80),     # 第一行 - 文件名，深蓝灰色（主要信息）
                QColor(52, 73, 94),     # 第二行 - 分辨率，中蓝灰色
                QColor(93, 109, 126),   # 第三行 - 时长，浅蓝灰色
                QColor(127, 140, 141)   # 第四行 - 文件大小，淡蓝灰色
            ]

            # 设置字体
            renderer.setFont(qt_font)

            # 绘制文本 - 保持原有的绘制方式
            for i, text in enumerate(info_texts):
                # 设置对应的颜色
                renderer.setPen(text_colors[i])

                # 创建文本矩形区域
                text_rect = QRect(
                    left_margin,
                    y_positions[i],
                    img_width - left_margin * 2,
                    line_height
                )

                # 绘制文本背景气泡（保持原有逻辑）
                # 计算文本宽度以确定气泡大小
                if i == 0:  # 标题行（第一行）处理
                    # 判断文件名中是否有冒号分隔符
                    colon_pos = text.find("：")
                    if colon_pos > 0:
                        # 如果有冒号，只为冒号后的部分添加气泡
                        label_width = font_metrics.horizontalAdvance(text[:colon_pos+1])
                        text_width = font_metrics.horizontalAdvance(text[colon_pos+1:])
                    else:
                        # 整个文件名都使用气泡效果
                        label_width = 0
                        text_width = font_metrics.horizontalAdvance(text)
                else:
                    # 其他行处理方式不变
                    text_width = font_metrics.horizontalAdvance(text.split("：")[1] if "：" in text else text)
                    colon_pos = text.find("：")
                    label_width = font_metrics.horizontalAdvance(text[:colon_pos+1]) if colon_pos > 0 else 0

                # 创建气泡矩形 - 只在冒号后或适当位置的内容周围
                bubble_rect = QRectF(
                    left_margin + label_width + 5,  # 标签文本后留一些间距
                    y_positions[i] - 1,
                    text_width + 20,  # 文本宽度加一些内边距
                    line_height + 2
                )

                # 绘制圆角背景 - 使用iOS风格的半透明效果
                bubble_path = QPainterPath()
                bubble_path.addRoundedRect(bubble_rect, 8, 8)  # iOS风格圆角

                # 设置iOS风格的背景 - 在清新背景上更清晰
                if i == 0:  # 文件名使用更明显的背景
                    bubble_color = QColor(255, 255, 255, 120)  # 半透明白色高光
                else:  # 其他信息使用更淡的背景
                    bubble_color = QColor(255, 255, 255, 80)   # 淡白色高光

                renderer.fillPath(bubble_path, QBrush(bubble_color))

                # 绘制文本
                try:
                    renderer.drawText(text_rect, Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter, text)
                except:
                    # 兼容性写法
                    renderer.drawText(text_rect, Qt.AlignLeft | Qt.AlignVCenter, text)
            
            # 结束绘制
            renderer.end()

            # 将QPixmap转换为QImage
            info_qimage = info_pixmap.toImage()

            # 将QImage转换为OpenCV图像
            bits = info_qimage.constBits()
            try:
                # PyQt6方式
                info_array = np.array(bits).reshape(info_bar_height, img_width, 4)
            except:
                # 兼容性处理
                bits.setsize(info_bar_height * img_width * 4)
                info_array = np.frombuffer(bits, np.uint8).reshape(info_bar_height, img_width, 4)

            # BGRA到BGR的转换
            info_array_bgr = cv2.cvtColor(info_array, cv2.COLOR_RGBA2BGR)

            # 直接在原图像上覆盖iOS风格信息区域，不增加高度
            result_img = display_img.copy()
            if info_position == 'top':
                # 在顶部覆盖信息
                result_img[0:info_bar_height, :] = info_array_bgr
            else:
                # 在底部覆盖信息
                start_y = max(0, img_height - info_bar_height)
                result_img[start_y:img_height, :] = info_array_bgr

            # 返回iOS风格的视频信息预览图
            return result_img

        except Exception as e:
            print(f"添加iOS风格视频信息时出错: {str(e)}")
            traceback.print_exc()
            return None

    def save_preview(self):
        """保存预览图"""
        if not hasattr(self, 'current_preview_img') or self.current_preview_img is None:
            self.status_label.setText("没有可用的预览图")
            self.status_label.setStyleSheet("color: red;")
            QTimer.singleShot(3000, lambda: self.status_label.clear())
            return
            
        try:
            # 获取保存路径
            save_dir = self.path_input.text()
            
            # 如果选择了保存到视频目录且有视频路径
            if hasattr(self, 'save_to_video_dir_checkbox') and self.save_to_video_dir_checkbox.isChecked() and hasattr(self, 'video_path') and self.video_path:
                save_dir = os.path.dirname(os.path.abspath(self.video_path))
                
            if not save_dir or not os.path.exists(save_dir):
                self.status_label.setText("请先设置有效的保存路径")
                self.status_label.setStyleSheet("color: red;")
                QTimer.singleShot(3000, lambda: self.status_label.clear())
                return
                
            # 获取视频文件名（不含扩展名）
            if self.video_path:
                video_name = os.path.splitext(os.path.basename(self.video_path))[0]
            else:
                video_name = "preview"
                
            # 生成保存文件名基础部分
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            file_base = f"{video_name}_preview_{timestamp}"
            
            # 获取保存格式
            format_type = self.save_format_combo.currentText().lower()
            
            # 获取当前预览设置
            if not hasattr(self, 'preview_dialog') or self.preview_dialog is None:
                self.preview_dialog = PreviewSettingsDialog(self)
            settings = self.preview_dialog.get_settings()
            
            # 保存导出前的原始预览布局和帧位置信息
            original_preview_img = None
            original_frame_positions = None
            if hasattr(self, 'current_preview_img'):
                original_preview_img = self.current_preview_img.copy()
            if hasattr(self, 'preview_frame_positions'):
                original_frame_positions = self.preview_frame_positions.copy()
            
            # 检查是否使用自定义导出布局
            if settings.get('use_custom_export_layout', False) and hasattr(self, 'preview_frame_positions') and self.preview_frame_positions:
                # 使用自定义导出布局
                export_rows = settings['export_rows']
                export_cols = settings['export_cols']
                frames_per_group = export_rows * export_cols
                
                # 将所有帧分组
                frame_groups = []
                for i in range(0, len(self.preview_frame_positions), frames_per_group):
                    group = self.preview_frame_positions[i:i+frames_per_group]
                    if group:  # 确保组不为空
                        frame_groups.append(group)
                
                if not frame_groups:
                    self.status_label.setText("没有有效的帧组可以导出")
                    self.status_label.setStyleSheet("color: red;")
                    QTimer.singleShot(3000, lambda: self.status_label.clear())
                    return
                
                # 为每个组生成一个预览图
                saved_count = 0
                for group_idx, group in enumerate(frame_groups):
                    # 构建组预览图保存路径
                    group_file_name = f"{file_base}_group{group_idx+1}.{format_type}"
                    group_save_path = os.path.join(save_dir, group_file_name)
                    
                    # 创建组预览图
                    group_preview = self.create_group_preview(group, settings)
                    
                    # 检查组预览图是否有效
                    if group_preview is None or group_preview.size == 0:
                        print(f"生成的组预览图 {group_idx+1} 无效，跳过保存")
                        continue
                    
                    try:
                        # 直接使用OpenCV保存预览图
                        print(f"正在保存预览图: {group_save_path}")
                        
                        # 根据格式设置保存参数
                        save_params = []
                        if format_type == 'png':
                            save_params = [cv2.IMWRITE_PNG_COMPRESSION, 0]  # 无损PNG
                        else:  # jpg
                            # 使用用户设置的JPG质量值
                            quality = self.jpg_quality_spin.value() if hasattr(self, 'jpg_quality_spin') else 100
                            save_params = [cv2.IMWRITE_JPEG_QUALITY, quality]
                            print(f"保存组预览图，使用JPG质量: {quality}")
                        
                        # 使用适合中文路径的方法保存
                        if cv2_imwrite_chinese(group_save_path, group_preview, save_params):
                            saved_count += 1
                            print(f"已保存高质量预览图: {group_save_path}")
                        else:
                            raise Exception("保存预览图失败")
                    except Exception as e:
                        print(f"保存组预览图 {group_idx+1} 失败: {str(e)}")
                        continue
                
                # 恢复原始预览布局和帧位置信息
                if original_preview_img is not None:
                    self.current_preview_img = original_preview_img
                if original_frame_positions is not None:
                    self.preview_frame_positions = original_frame_positions
                
                if saved_count > 0:
                    # 显示成功消息
                    self.status_label.setText(f"已保存 {saved_count} 个预览图到: {save_dir}")
                    self.status_label.setStyleSheet("color: green;")
                    QTimer.singleShot(5000, lambda: self.status_label.clear())
                else:
                    self.status_label.setText("没有预览图被保存")
                    self.status_label.setStyleSheet("color: red;")
                    QTimer.singleShot(3000, lambda: self.status_label.clear())
                
            else:
                # 使用默认布局（单个预览图）
                save_path = os.path.join(save_dir, f"{file_base}.{format_type}")
                
                # 根据是否显示视频信息选择要保存的图像
                save_img = None
                if hasattr(self, 'display_preview_with_info') and self.display_preview_with_info is not None and settings.get('show_video_info', False):
                    save_img = self.display_preview_with_info
                else:
                    save_img = self.current_preview_img
                
                # 检查图像是否有效
                if save_img is None:
                    self.status_label.setText("没有有效的预览图可保存")
                    self.status_label.setStyleSheet("color: red;")
                    QTimer.singleShot(3000, lambda: self.status_label.clear())
                    return
                
                try:
                    # 直接使用OpenCV保存预览图
                    print(f"正在保存预览图: {save_path}")
                    
                    # 根据格式设置保存参数
                    save_params = []
                    if format_type == 'png':
                        save_params = [cv2.IMWRITE_PNG_COMPRESSION, 0]  # 无损PNG
                    else:  # jpg
                        # 使用用户设置的JPG质量值
                        quality = self.jpg_quality_spin.value() if hasattr(self, 'jpg_quality_spin') else 100
                        save_params = [cv2.IMWRITE_JPEG_QUALITY, quality]
                        print(f"保存预览图，使用JPG质量: {quality}")
                    
                    # 使用适合中文路径的方法保存
                    if cv2_imwrite_chinese(save_path, save_img, save_params):
                        # 显示成功消息
                        self.status_label.setText(f"预览图已保存到: {save_path}")
                        self.status_label.setStyleSheet("color: green;")
                        QTimer.singleShot(5000, lambda: self.status_label.clear())
                        print(f"已保存高质量预览图: {save_path}")
                    else:
                        raise Exception("保存预览图失败")
                except Exception as e:
                    error_msg = f"保存预览图时出错: {str(e)}"
                    print(error_msg)
                    self.status_label.setText(error_msg)
                    self.status_label.setStyleSheet("color: red;")
                    QTimer.singleShot(3000, lambda: self.status_label.clear())
                    return
            
        except Exception as e:
            self.status_label.setText(f"保存预览图失败: {str(e)}")
            self.status_label.setStyleSheet("color: red;")
            QTimer.singleShot(3000, lambda: self.status_label.clear())
            print(f"保存预览图失败: {str(e)}")
            traceback.print_exc()
    

    
    def create_group_preview(self, frame_group, settings):
        """根据帧组和设置创建预览图 - 每个帧使用与单帧保存完全相同的方式"""
        try:
            import numpy as np
            import tempfile
            import os
            import subprocess
            import traceback
            import cv2
            
            # 计算预览图尺寸
            rows = settings['export_rows']
            cols = settings['export_cols']
            h_spacing = settings['h_spacing']
            v_spacing = settings['v_spacing']
            top_margin = settings['top_margin']
            bottom_margin = settings['bottom_margin']
            left_margin = settings['left_margin']
            right_margin = settings['right_margin']
            
            # 获取单元格尺寸（使用第一个帧的尺寸）
            if frame_group and len(frame_group) > 0:
                cell_width = frame_group[0].get('width', 160)
                cell_height = frame_group[0].get('height', 90)
            else:
                cell_width = 160  # 默认单元格宽度
                cell_height = 90   # 默认单元格高度
            
            # 计算总宽度和高度
            total_width = left_margin + cols * cell_width + (cols - 1) * h_spacing + right_margin
            total_height = top_margin + rows * cell_height + (rows - 1) * v_spacing + bottom_margin
            
            # 创建预览图
            preview_img = np.zeros((total_height, total_width, 3), dtype=np.uint8)
            
            # 设置背景
            if settings.get('use_bg_image', False) and (
                ('bg_image_data' in settings and settings['bg_image_data'] is not None) or 
                (settings.get('bg_image_path', '') and os.path.exists(settings['bg_image_path']))
            ):
                # 使用背景图片
                bg_img = None
                if 'bg_image_data' in settings and settings['bg_image_data'] is not None:
                    bg_img = settings['bg_image_data'].copy()
                else:
                    try:
                        bg_img = cv2_imread_chinese(settings['bg_image_path'])
                        if bg_img is None:
                            raise Exception("无法读取背景图片")
                        print(f"使用OpenCV读取背景图: {settings['bg_image_path']}")
                    except Exception as e:
                        print(f"加载背景图失败: {str(e)}")
                        bg_img = None
                
                if bg_img is not None:
                    # 处理背景图
                    if settings.get('tile_bg', True):
                        # 平铺背景
                        bg_h, bg_w = bg_img.shape[:2]
                        for y in range(0, total_height, bg_h):
                            for x in range(0, total_width, bg_w):
                                y_end = min(y + bg_h, total_height)
                                x_end = min(x + bg_w, total_width)
                                h_slice = slice(y, y_end)
                                w_slice = slice(x, x_end)
                                src_h_slice = slice(0, y_end - y)
                                src_w_slice = slice(0, x_end - x)
                                preview_img[h_slice, w_slice] = bg_img[src_h_slice, src_w_slice]
                    else:
                        # 缩放背景
                        if settings.get('keep_ratio', True):
                            # 保持比例
                            bg_h, bg_w = bg_img.shape[:2]
                            ratio = min(total_width / bg_w, total_height / bg_h)
                            new_w = int(bg_w * ratio)
                            new_h = int(bg_h * ratio)
                            bg_img = cv2.resize(bg_img, (new_w, new_h), interpolation=cv2.INTER_AREA)
                            
                            # 居中放置
                            y_offset = (total_height - new_h) // 2
                            x_offset = (total_width - new_w) // 2
                            preview_img[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = bg_img
                        else:
                            # 拉伸填充
                            bg_img = cv2.resize(bg_img, (total_width, total_height), interpolation=cv2.INTER_AREA)
                            preview_img[:] = bg_img
                    
                        # 应用透明度
                        opacity = settings.get('bg_opacity', 1.0)
                        if opacity < 1.0:
                            bg_color = settings.get('bg_color', None)
                            if bg_color:
                                # 创建纯色背景
                                color_bg = np.zeros_like(preview_img)
                                color_bg[:] = (bg_color.blue(), bg_color.green(), bg_color.red())
                                # 混合背景
                                preview_img = cv2.addWeighted(preview_img, opacity, color_bg, 1.0 - opacity, 0)
            else:
                # 使用纯色背景
                bg_color = settings.get('bg_color', None)
                if bg_color:
                    preview_img[:] = (bg_color.blue(), bg_color.green(), bg_color.red())
            
            # 不添加边框效果
            
            # 使用直接绘制方式填充预览图
            print(f"使用直接绘制方式创建预览图，帧组大小: {len(frame_group)} 个帧")
            valid_frames = 0  # 计数成功添加的帧
            
            for idx, frame_info in enumerate(frame_group):
                if idx >= rows * cols:
                    break  # 超出布局范围
                
                # 计算行列位置
                row_idx = idx // cols
                col_idx = idx % cols
                
                # 计算当前缩略图位置
                x = left_margin + col_idx * (cell_width + h_spacing)
                y = top_margin + row_idx * (cell_height + v_spacing)
                
                try:
                    # 直接使用帧的位置信息从原始预览图中提取
                    if hasattr(self, 'current_preview_img') and self.current_preview_img is not None:
                        # 尝试获取帧在大图中的位置
                        source_x = frame_info.get('x', 0)
                        source_y = frame_info.get('y', 0)
                        source_w = frame_info.get('width', cell_width)
                        source_h = frame_info.get('height', cell_height)
                        
                        # 检查源位置是否有效
                        if source_x >= 0 and source_y >= 0:
                            # 检查源图像是否足够大
                            current_img = self.current_preview_img
                            h, w = current_img.shape[:2]
                            
                            if h >= source_y + source_h and w >= source_x + source_w:
                                # 提取区域
                                frame = current_img[source_y:source_y+source_h, source_x:source_x+source_w].copy()
                                
                                # 调整大小
                                if frame.shape[0] != cell_height or frame.shape[1] != cell_width:
                                    frame = cv2.resize(frame, (cell_width, cell_height), interpolation=cv2.INTER_AREA)
                                
                                # 将区域放入预览图
                                preview_img[y:y+cell_height, x:x+cell_width] = frame
                                print(f"已将帧 {idx+1} 放入预览图，来源位置: ({source_x}, {source_y})")
                                valid_frames += 1
                                continue
                            else:
                                print(f"帧 {idx+1} 的源位置 ({source_x}, {source_y}) 超出图像范围 ({w}x{h})")
                    
                    # 如果无法获取帧，绘制占位框
                    cv2.rectangle(preview_img[y:y+cell_height, x:x+cell_width], (0, 0), 
                                 (cell_width-1, cell_height-1), (0, 255, 0), 1)
                    cv2.putText(preview_img[y:y+cell_height, x:x+cell_width], f"帧 {idx+1}", 
                               (10, cell_height//2), cv2.FONT_HERSHEY_SIMPLEX, 
                               0.5, (0, 0, 255), 1, cv2.LINE_AA)
                    print(f"为帧 {idx+1} 创建了占位框")
                
                except Exception as e:
                    print(f"处理帧 {idx+1} 失败: {str(e)}")
                    # 绘制错误框
                    cv2.rectangle(preview_img[y:y+cell_height, x:x+cell_width], (0, 0), 
                                 (cell_width-1, cell_height-1), (0, 0, 255), 1)
                    cv2.putText(preview_img[y:y+cell_height, x:x+cell_width], "错误", 
                               (cell_width//4, cell_height//2), cv2.FONT_HERSHEY_SIMPLEX, 
                               0.5, (0, 0, 255), 1, cv2.LINE_AA)
            
            # 输出处理结果
            print(f"成功放置 {valid_frames}/{len(frame_group)} 个帧到预览图")
            
            # 保存当前预览图，以便后续应用替换的帧
            temp_preview_img = preview_img.copy()
            
            # 返回预览图
            return preview_img
            
        except Exception as e:
            print(f"创建组预览图失败: {str(e)}")
            traceback.print_exc()
            return None
        
    def display_preview_image(self, image):
        """显示预览图"""
        if image is None or not hasattr(self, 'preview_area'):
            return
            
        try:
            h, w = image.shape[:2]
            q_image = self._convert_cv_to_qt_image(image)
            if q_image is None:
                return
                
            pixmap = QPixmap.fromImage(q_image)
            
            # 获取固定宽高
            preview_width = self.preview_area.width()
            preview_height = self.preview_area.height()
            
            # 计算保持纵横比的尺寸
            scaled_pixmap = pixmap.scaled(
                preview_width - 20,
                preview_height - 20,
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )
            
            # 创建一个绘图器来绘制带边框和圆角的图像
            final_pixmap = QPixmap(scaled_pixmap.size())
            final_pixmap.fill(Qt.GlobalColor.transparent)  # 透明背景
            
            painter = QPainter(final_pixmap)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)  # 抗锯齿
            
            # 绘制白色背景作为底层
            painter.setBrush(QColor("#ffffff"))
            painter.setPen(Qt.PenStyle.NoPen)
            painter.drawRoundedRect(1, 1, scaled_pixmap.width()-2, scaled_pixmap.height()-2, 8, 8)
            
            # 绘制原始图像，稍微缩小一点留出边框
            painter.drawPixmap(1, 1, scaled_pixmap.width()-2, scaled_pixmap.height()-2, scaled_pixmap)
            
            # 添加细腻的边框
            painter.setBrush(Qt.BrushStyle.NoBrush)
            painter.setPen(QPen(QColor("#eeeeee"), 1))
            painter.drawRoundedRect(0, 0, scaled_pixmap.width(), scaled_pixmap.height(), 8, 8)
            
            painter.end()
            
            # 显示图像
            self.preview_area.setPixmap(final_pixmap)
            
            # 更新预览帧位置信息
            if hasattr(self, 'current_preview_frames') and self.current_preview_frames:
                self.preview_frame_positions = self.current_preview_frames.copy()
            
        except Exception as e:
            print(f"显示预览图时出错: {str(e)}")

    def _convert_cv_to_qt_image(self, cv_image):
        """将OpenCV图像转换为Qt图像"""
        if cv_image is None:
            return None
            
        h, w = cv_image.shape[:2]
        rgb_image = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)
        bytes_per_line = 3 * w
        return QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format.Format_RGB888)

    def video_area_wheel_event(self, event):
        """处理视频区域的鼠标滚轮事件"""
        if not self.decoder or self.total_frames <= 0:
            return
            
        # 获取当前帧位置
        current_pos = self.slider.value()
        
        # 使用固定的0.1秒间隔
        frames_to_move = max(1, int(0.1 * self.fps))
        
        # 根据滚轮方向计算新的帧位置
        delta = event.angleDelta().y()
        if delta > 0:  # 向上滚动，后退
            new_pos = max(0, current_pos - frames_to_move)
        else:  # 向下滚动，前进
            new_pos = min(self.total_frames - 1, current_pos + frames_to_move)
        
        # 更新滑块位置
        self.slider.setValue(new_pos)
        
        # 使用OpenCV定位到指定帧
        if self.decoder.cap and self.decoder.cap.isOpened():
            self.decoder.cap.set(cv2.CAP_PROP_POS_FRAMES, new_pos)
            ret, frame = self.decoder.cap.read()
            if ret:
                # 更新缓存
                self.decoder.frame_buffer[new_pos] = frame
                # 限制缓存大小
                if len(self.decoder.frame_buffer) > self.decoder.buffer_size:
                    self.decoder.frame_buffer.popitem(last=False)
                self.decoder.current_pos = new_pos
                
                # 显示帧
                self.display_frame(frame)
                self.update_time_display(new_pos)
        
        event.accept()

    def update_save_path(self):
        """根据复选框状态更新保存路径"""
        if self.save_to_video_dir_checkbox.isChecked() and hasattr(self, 'video_path') and self.video_path:
            # 获取视频所在目录
            video_dir = os.path.dirname(os.path.abspath(self.video_path))
            self.path_input.setText(video_dir)
            self.path_input.setEnabled(False)  # 禁用路径输入框
            self.browse_btn.setEnabled(False)  # 禁用浏览按钮
        else:
            self.path_input.setEnabled(True)  # 启用路径输入框
            self.browse_btn.setEnabled(True)  # 启用浏览按钮

    def save_replaced_frames(self):
        """保存当前视频的替换帧记录"""
        if not hasattr(self, 'video_path') or not self.video_path:
            return
            
        try:
            # 获取视频文件大小
            video_size = os.path.getsize(self.video_path)
            # 获取视频时长（秒）
            video_duration = self.total_frames / self.fps if hasattr(self, 'total_frames') and hasattr(self, 'fps') else 0
            
            # 使用视频大小和时长作为标识
            video_id = hashlib.md5(f"{video_size}_{video_duration:.3f}".encode()).hexdigest()
            record_file = os.path.join(self.replaced_frames_dir, f"{video_id}.json")
            
            # 准备要保存的数据，添加FPS信息
            save_data = {
                'video_path': os.path.abspath(self.video_path),
                'video_size': video_size,
                'video_duration': video_duration,
                'fps': self.fps,  # 添加FPS信息以便精确定位
                'replaced_frames': {}
            }
            
            # 转换替换帧数据为可序列化格式，添加精确时间信息
            for pos_index, replacement in self.replaced_frames.items():
                # 计算精确时间（秒）
                frame_index = replacement['current_frame_index']
                exact_seconds = frame_index / self.fps
                
                save_data['replaced_frames'][str(pos_index)] = {
                    'current_frame_index': int(frame_index),  # 确保使用整数
                    'original_frame_index': int(replacement['original_frame_index']),
                    'position': replacement['position'],
                    'exact_seconds': exact_seconds  # 添加精确时间信息
                }
                print(f"保存替换帧 {pos_index}: 帧索引={frame_index}, 时间={exact_seconds:.3f}秒")
            
            # 保存到文件
            with open(record_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
                
            print(f"已保存替换帧记录到: {record_file}")
            
        except Exception as e:
            print(f"保存替换帧记录失败: {str(e)}")
            traceback.print_exc()

    def load_replaced_frames(self):
        """加载当前视频的替换帧记录"""
        if not hasattr(self, 'video_path') or not self.video_path:
            return
            
        try:
            # 获取当前视频的信息
            video_size = os.path.getsize(self.video_path)
            video_duration = self.total_frames / self.fps if hasattr(self, 'total_frames') and hasattr(self, 'fps') else 0
            
            # 使用视频大小和时长作为标识
            video_id = hashlib.md5(f"{video_size}_{video_duration:.3f}".encode()).hexdigest()
            record_file = os.path.join(self.replaced_frames_dir, f"{video_id}.json")
            
            if not os.path.exists(record_file):
                # 尝试查找旧的记录文件（基于路径的）
                old_video_hash = hashlib.md5(os.path.abspath(self.video_path).encode()).hexdigest()
                old_record_file = os.path.join(self.replaced_frames_dir, f"{old_video_hash}.json")
                if os.path.exists(old_record_file):
                    # 如果找到旧的记录文件，迁移到新格式
                    with open(old_record_file, 'r', encoding='utf-8') as f:
                        old_data = json.load(f)
                    # 添加新的标识信息
                    old_data['video_size'] = video_size
                    old_data['video_duration'] = video_duration
                    # 保存为新格式
                    with open(record_file, 'w', encoding='utf-8') as f:
                        json.dump(old_data, f, ensure_ascii=False, indent=2)
                    # 删除旧文件
                    try:
                        os.remove(old_record_file)
                    except:
                        pass
                else:
                    return
                
            # 加载记录文件
            with open(record_file, 'r', encoding='utf-8') as f:
                save_data = json.load(f)
                
            # 验证视频大小和时长
            if abs(save_data.get('video_size', 0) - video_size) > 100:  # 允许100字节的误差
                print("视频文件大小不匹配，跳过加载替换帧记录")
                return
                
            if abs(save_data.get('video_duration', 0) - video_duration) > 0.1:  # 允许0.1秒的误差
                print("视频时长不匹配，跳过加载替换帧记录")
                return
                
            # 清除现有的替换帧记录
            self.replaced_frames = {}
            
            # 恢复替换帧数据
            for pos_index_str, replacement in save_data['replaced_frames'].items():
                pos_index = int(pos_index_str)
                # 使用精确帧定位获取替换帧
                frame_index = replacement['current_frame_index']
                print(f"尝试精确定位替换帧 {frame_index}")
                frame = self.decoder.seek_frame_precise(frame_index)
                if frame is not None:
                    # 调整帧大小以匹配位置信息
                    pos = replacement['position']
                    frame = cv2.resize(frame, (pos['width'], pos['height']), interpolation=cv2.INTER_AREA)
                    # 保存替换帧信息
                    self.replaced_frames[pos_index] = {
                        'frame_data': frame,
                        'current_frame_index': replacement['current_frame_index'],
                        'original_frame_index': replacement['original_frame_index'],
                        'position': pos
                    }
            
            print(f"已加载 {len(self.replaced_frames)} 个替换帧记录")
            
            # 应用替换帧
            if hasattr(self, 'current_preview_img'):
                self.apply_replaced_frames()
                
        except Exception as e:
            print(f"加载替换帧记录失败: {str(e)}")
            traceback.print_exc()

    def restore_default_frames(self):
        """恢复所有被替换的帧到默认状态"""
        if not hasattr(self, 'replaced_frames') or not self.replaced_frames:
            self.status_label.setText("没有可恢复的默认帧")
            self.status_label.setStyleSheet("color: orange;")
            QTimer.singleShot(2000, lambda: self.status_label.clear())
            return
        
        try:
            # 清空替换帧字典
            self.replaced_frames = {}
            
            # 重新生成预览图
            settings = None
            if hasattr(self, 'preview_dialog') and self.preview_dialog:
                settings = self.preview_dialog.get_settings()
            else:
                settings = PreviewSettingsDialog.last_settings.copy()
                
            # 重新生成预览图
            self.update_preview_now()
            
            # 显示成功提示
            self.status_label.setText("已恢复所有默认帧")
            self.status_label.setStyleSheet("color: green;")
            QTimer.singleShot(2000, lambda: self.status_label.clear())
            
        except Exception as e:
            self.status_label.setText(f"恢复默认帧失败: {str(e)}")
            self.status_label.setStyleSheet("color: red;")
            QTimer.singleShot(3000, lambda: self.status_label.clear())
            print(f"恢复默认帧失败: {str(e)}")

class BatchManager:
    def __init__(self, positions, parent):
        self.positions = positions
        self.current_index = 0
        self.parent = parent
        self.current_thread = None
        self.results = []
        self.is_cancelled = False
        self.cancel_timer = QTimer()
        self.cancel_timer.timeout.connect(self.check_cancel_finished)
        self.cancel_timer.setInterval(100)  # 每100ms检查一次
        
    def _generate_save_path(self, base_path, index):
        """生成带序号的保存路径"""
        dir_path = os.path.dirname(base_path)
        file_name = os.path.basename(base_path)
        name, ext = os.path.splitext(file_name)
        return os.path.join(dir_path, f"{name}_{index + 1}{ext}")
        
    def start_next(self):
        """开始处理下一个位置"""
        if self.is_cancelled or self.current_index >= len(self.positions):
            self.finish_processing()
            return
            
        try:
            # 准备参数
            pos = self.positions[self.current_index]
            params = self.parent.prepare_animation_params(pos)
            
            if params is None:
                raise Exception("参数准备失败")
            
            # 修改保存路径，添加序号
            params['save_path'] = self._generate_save_path(params['save_path'], self.current_index)
            
            # 创建并启动线程
            self.current_thread = AnimationThread(params)
            self.current_thread.progress_signal.connect(self.update_progress)
            self.current_thread.result_signal.connect(self.handle_result)
            self.current_thread.start()
            
        except Exception as e:
            self.handle_error(f"处理位置 {self.current_index + 1} 时出错: {str(e)}")
    
    def update_progress(self, progress_data):
        """更新进度"""
        value, description = progress_data
        total_progress = self.current_index * 100 + value
        max_progress = len(self.positions) * 100
        percentage = min(100, int((total_progress * 100) / max_progress))
        
        self.parent.progress_bar.setValue(total_progress)
        self.parent.progress_bar.setFormat(
            f"正在处理第 {self.current_index + 1}/{len(self.positions)} 个动画: {description} ({percentage}%)"
        )
    
    def handle_result(self, result_data):
        """处理结果"""
        if self.is_cancelled:
            self.cleanup()
            return
            
        success, message, data = result_data
        
        if success:
            self.results.append({
                'position': self.positions[self.current_index],
                'path': data.get('file_path', ''),
                'success': True
            })
            
            # 等待当前线程完成并清理
            if self.current_thread:
                self.current_thread.wait()
                self.current_thread.deleteLater()
                self.current_thread = None
            
            # 继续处理下一个
            self.current_index += 1
            self.start_next()
        else:
            self.handle_error(f"位置 {self.current_index + 1} 处理失败: {message}")
    
    def handle_error(self, error_message):
        """处理错误"""
        self.parent.status_label.setText(error_message)
        self.parent.status_label.setStyleSheet("color: red;")
        self.cleanup()
    
    def finish_processing(self):
        """完成所有处理"""
        success_count = sum(1 for r in self.results if r['success'])
        message = f"完成 {success_count}/{len(self.positions)} 个动画制作"
        
        self.parent.status_label.setText(message)
        self.parent.status_label.setStyleSheet("color: green;")
        QTimer.singleShot(3000, lambda: self.parent.status_label.clear())
        
        self.cleanup()
    
    def cleanup(self):
        """清理资源并恢复界面状态"""
        # 确保当前线程被正确清理
        if self.current_thread:
            if self.current_thread.isRunning():
                self.current_thread.cancel()
                self.current_thread.wait()
            self.current_thread.deleteLater()
            self.current_thread = None
            
        if self.is_cancelled:
            self.parent.status_label.setText("已取消动画制作")
            self.parent.status_label.setStyleSheet("color: orange;")
            # 3秒后清除状态信息
            QTimer.singleShot(3000, lambda: self.parent.status_label.clear())

        self.parent.progress_bar.setVisible(False)
        self.parent.progress_bar.setValue(0)  # 重置进度条
        self.parent.make_gif_btn.setEnabled(True)
        self.parent.add_position_btn.setEnabled(True)
        self.parent.remove_position_btn.setEnabled(True)
        self.parent.clear_positions_btn.setEnabled(True)
        self.parent.cancel_btn.setEnabled(False)

        # 停止定时器
        if hasattr(self, 'cancel_timer'):
            self.cancel_timer.stop()
    
    def cancel(self):
        """异步取消方法"""
        self.is_cancelled = True
        if self.current_thread:
            self.current_thread.cancel()  # 发送取消信号
            self.cancel_timer.start()  # 启动检查定时器
        else:
            self.cleanup()

    def check_cancel_finished(self):
        """检查取消是否完成"""
        if not self.current_thread or not self.current_thread.isRunning():
            self.cancel_timer.stop()
            self.cleanup()
            # 删除可能存在的未完成文件
            if self.current_thread and hasattr(self.current_thread, 'params'):
                try:
                    save_path = self.current_thread.params.get('save_path')
                    if save_path and os.path.exists(save_path):
                        os.remove(save_path)
                except Exception as e:
                    print(f"删除未完成文件时出错: {str(e)}")

class KeyframeInitWorker(QThread):
    finished = pyqtSignal(list)  # 发送关键帧列表
    
    def __init__(self, video_path, fps):
        super().__init__()
        self.video_path = video_path
        self.fps = fps
        
    def run(self):
        try:
            keyframes = []
            cap = cv2.VideoCapture(self.video_path)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # 每秒保存一个关键帧位置
            interval = int(self.fps)
            for frame_pos in range(0, total_frames, interval):
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_pos)
                if cap.grab():  # 只获取帧，不解码
                    keyframes.append(frame_pos)
            
            cap.release()
            self.finished.emit(keyframes)
            
        except Exception as e:
            print(f"初始化关键帧失败: {str(e)}")
            self.finished.emit([])

# 添加在文件开头的导入部分
import numpy as np
import cv2

def cv2_imread_chinese(file_path):
    """处理中文路径的图片读取"""
    try:
        # 将路径转换为字节流
        img_mat = np.fromfile(file_path, dtype=np.uint8)
        # 解码图片
        img = cv2.imdecode(img_mat, cv2.IMREAD_COLOR)
        if img is None:
            raise Exception("无法解码图片")
        return img
    except Exception as e:
        print(f"读取图片失败: {str(e)}")
        return None

def cv2_imwrite_chinese(file_path, img, params=None):
    """处理中文路径的图片保存"""
    try:
        # 确保目录存在
        dir_path = os.path.dirname(os.path.abspath(file_path))
        if not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)
            
        # 编码图片数据
        ext = os.path.splitext(file_path)[1]
        if params is None:
            params = []
            
        # 根据扩展名确定格式并编码
        img_encode = cv2.imencode(ext, img, params)[1]
            
        # 直接写入文件
        with open(file_path, 'wb') as f:
            img_encode.tofile(f)
        return True
    except Exception as e:
        print(f"保存图片失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

class StitchWorker(QThread):
    progress_signal = pyqtSignal(int, str)  # 进度信号
    finished_signal = pyqtSignal(bool, str, dict)  # 完成信号
    
    def __init__(self, positions, params, decoder, fps):
        super().__init__()
        self.positions = positions
        self.params = params
        self.decoder = decoder
        self.fps = fps
        self.is_cancelled = False

    def run(self):
        try:
            print("\n=== 开始拼接处理 ===")
            print(f"质量模式: {self.params['quality_mode']}")
            print(f"格式类型: {self.params['format_type']}")
            print(f"位置数量: {len(self.positions)}")
            
            if not self.positions or not self.decoder:
                raise Exception("没有有效的位置或视频未加载")

            # 计算参数
            segment_frames = int(self.params['duration'] * self.params['target_fps'])
            total_segments = len(self.positions)
            print(f"每段帧数: {segment_frames}")
            print(f"总段数: {total_segments}")
            
            # 获取目标尺寸
            if self.params['resize']:
                frame_width = self.params['target_width']
                frame_height = self.params['target_height']
            else:
                first_frame = self.decoder.seek_frame(self.positions[0])
                if first_frame is None:
                    raise Exception("无法读取视频帧")
                frame_height, frame_width = first_frame.shape[:2]
            print(f"输出尺寸: {frame_width}x{frame_height}")

            # 使用 FFmpeg 直接处理视频
            ffmpeg_path = get_ffmpeg_path()
            print(f"FFmpeg 路径: {ffmpeg_path}")
            
            # 构建 FFmpeg 命令
            inputs = []
            filter_complex = []
            
            print("\n=== 构建过滤器链 ===")
            # 为每个位置创建输入流
            for i, start_pos in enumerate(self.positions):
                start_time = start_pos / self.fps
                inputs.extend([
                    '-ss', str(start_time),
                    '-t', str(self.params['duration']),
                    '-i', self.params['video_path']
                ])
                print(f"添加输入流 {i+1}/{total_segments}: 开始时间={start_time}秒")
                
                # 修改过滤器，确保所有流保持动态
                filter_complex.append(
                    f'[{i}:v]scale={frame_width}:{frame_height}:flags=lanczos,'
                    f'fps={self.params["target_fps"]}:round=near,'
                    f'setpts=PTS-STARTPTS[v{i}];'  # 添加时间戳重置
                )

            # 修改堆叠过滤器
            stack_inputs = ''.join(f'[v{i}]' for i in range(total_segments))
            filter_complex.append(f'{stack_inputs}hstack=inputs={total_segments}:shortest=0[out]')  # 添加 shortest=0

            # 构建完整的 FFmpeg 命令
            ffmpeg_cmd = [
                ffmpeg_path, '-y',
                *inputs,
                '-filter_complex', ''.join(filter_complex),
                '-map', '[out]',
                '-c:v', 'libwebp'
            ]

            # 添加循环设置
            ffmpeg_cmd.extend([
                '-loop', str(0 if self.params['loop_count'] == 0 else self.params['loop_count'])
            ])

            # 根据质量模式设置 WebP 参数
            if self.params['quality_mode'] == "完全无损":
                ffmpeg_cmd.extend([
                    '-lossless', '1',
                    '-quality', '100',
                    '-compression_level', '6',
                    '-preset', 'photo',
                    '-pix_fmt', 'rgb24',
                    '-vsync', '0',
                    '-auto-alt-ref', '0'
                ])
            elif self.params['quality_mode'] == "高质量":
                ffmpeg_cmd.extend([
                    '-lossless', '0',
                    '-quality', '95',
                    '-compression_level', '5',
                    '-preset', 'photo',
                    '-qmin', '1',
                    '-qmax', '10'
                ])
            elif self.params['quality_mode'] == "平衡":
                ffmpeg_cmd.extend([
                    '-lossless', '0',
                    '-quality', '90',
                    '-compression_level', '4',
                    '-preset', 'photo',
                    '-qmin', '5',
                    '-qmax', '30'
                ])
            else:  # 快速模式
                ffmpeg_cmd.extend([
                    '-lossless', '0',
                    '-quality', '75',
                    '-compression_level', '3',
                    '-preset', 'drawing',
                    '-qmin', '10',
                    '-qmax', '50'
                ])

            # 添加必要的参数，确保不重新编码
            ffmpeg_cmd.extend([
                '-vsync', '0',  # 禁用视频同步以避免重新编码
                '-copyts',  # 复制时间戳
                '-avoid_negative_ts', 'make_zero',  # 避免负时间戳
                '-loop', str(0 if self.params['loop_count'] == 0 else self.params['loop_count'])
            ])

            # 添加输出文件
            ffmpeg_cmd.append(self.params['save_path'])
            
            print("\n=== FFmpeg 命令 ===")
            print(' '.join(ffmpeg_cmd))
            
            # 执行 FFmpeg 命令
            print("\n=== 开始执行 FFmpeg ===")
            total_duration = self.params['duration']
            total_frames = int(total_duration * self.params['target_fps'])
            total_segments = len(self.positions)
            total_steps = total_frames * total_segments
            current_frame = 0
            current_segment = 0

            self.progress_signal.emit(20, "开始处理视频片段...")

            process = subprocess.Popen(
                ffmpeg_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                creationflags=subprocess.CREATE_NO_WINDOW,
                encoding='utf-8'  # 添加 UTF-8 编码设置
            )

            # 监控进度
            while True:
                if self.is_cancelled:
                    process.terminate()
                    process.wait()  # 等待进程完全终止
                    self.progress_signal.emit(0, "已取消")
                    self.finished_signal.emit(False, "用户取消操作", {})
                    return  # 直接返回，不抛出异常
                    
                if process.poll() is not None:
                    break
                    
                line = process.stderr.readline()
                if line:
                    print(f"FFmpeg: {line.strip()}")
                    if "frame=" in line:
                        try:
                            frame_match = re.search(r'frame=\s*(\d+)', line)
                            time_match = re.search(r'time=\s*(\d+):(\d+):(\d+.\d+)', line)
                            
                            if frame_match:
                                current_frame = int(frame_match.group(1))
                                current_segment = current_frame // total_frames
                                segment_frame = current_frame % total_frames
                                total_progress = min(95, int((current_frame * 95) / total_steps))
                                progress_msg = (
                                    f"正在处理第 {current_segment + 1}/{total_segments} 个片段: "
                                    f"帧 {segment_frame + 1}/{total_frames} "
                                    f"(总进度: {total_progress}%)"
                                )
                                self.progress_signal.emit(total_progress, progress_msg)
                                
                            elif time_match:
                                hours = int(time_match.group(1))
                                minutes = int(time_match.group(2))
                                seconds = float(time_match.group(3))
                                current_time = hours * 3600 + minutes * 60 + seconds
                                current_segment = int(current_time // total_duration)
                                segment_time = current_time % total_duration
                                total_progress = min(95, int((current_time * 95) / (total_duration * total_segments)))
                                progress_msg = (
                                    f"正在处理第 {current_segment + 1}/{total_segments} 个片段: "
                                    f"{int(segment_time)}/{int(total_duration)}秒 "
                                    f"(总进度: {total_progress}%)"
                                )
                                self.progress_signal.emit(total_progress, progress_msg)
                        except Exception as e:
                            print(f"解析进度时出错: {str(e)}")
                
                time.sleep(0.1)

            # 处理完成后的步骤
            self.progress_signal.emit(96, "正在合并片段...")
            time.sleep(0.5)
            self.progress_signal.emit(98, "正在优化输出...")
            time.sleep(0.5)
            self.progress_signal.emit(100, "处理完成")

            print("拼接处理成功完成！")
            self.finished_signal.emit(True, "拼接动画制作完成", {
                'fps': self.params['target_fps'],
                'duration': self.params['duration'],
                'file_path': self.params['save_path']
            })
            
        except Exception as e:
            if not self.is_cancelled:  # 只在非取消状态下发送错误信息
                print(f"\n=== 错误 ===\n{str(e)}")
                self.finished_signal.emit(False, str(e), {})
            else:
                self.finished_signal.emit(False, "用户取消操作", {})

class DraggableListWidget(QListWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setDragDropMode(QListWidget.DragDropMode.InternalMove)
        self.setSelectionMode(QListWidget.SelectionMode.SingleSelection)
        self.itemDoubleClicked.connect(self.preview_item)
        self.main_window = None  # 添加 main_window 属性
        
    def set_main_window(self, window):
        """设置主窗口引用"""
        self.main_window = window
        
    def preview_item(self, item):
        """预览选中的动画位置"""
        if self.main_window and item:
            frame_pos = item.data(Qt.ItemDataRole.UserRole)
            if frame_pos is not None:
                self.main_window.slider.setValue(frame_pos)

    def mousePressEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_start_position = event.pos()
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        if not (event.buttons() & Qt.MouseButton.LeftButton):
            return
            
        if (event.pos() - self.drag_start_position).manhattanLength() < QApplication.startDragDistance():
            return
            
        current_item = self.currentItem()
        if current_item:
            # 处理拖动
            super().mouseMoveEvent(event)

    def dropEvent(self, event):
        super().dropEvent(event)
        # 更新所有项的显示文本
        self.update_item_numbers()
        
    def update_item_numbers(self):
        """更新所有项的编号"""
        if not self.main_window:
            return
            
        for i in range(self.count()):
            item = self.item(i)
            frame_pos = item.data(Qt.ItemDataRole.UserRole)
            timestamp = self.main_window.frame_to_timestamp(frame_pos)
            item.setText(f"位置 {i + 1}: {timestamp} (帧: {frame_pos})")

class PreviewSettingsDialog(QDialog):
    # 添加类变量保存上次的设置
    last_settings = {
        'rows': 4,
        'cols': 4,
        'h_spacing': 10,
        'v_spacing': 10,
        'top_margin': 20,
        'bottom_margin': 20,
        'left_margin': 20,
        'right_margin': 20,
        'bg_color': None,  # 将在初始化时创建为白色
        'use_bg_image': False,
        'bg_image_path': "",
        'tile_bg': True,
        'keep_ratio': True,
        'bg_opacity': 1.0,
        'use_custom_export_layout': False,
        'export_rows': 1,
        'export_cols': 4,
        'show_video_info': True,
        'info_font_size': 14,
        'info_position': 'top',
        'info_bg_color': None,  # 信息背景颜色，将在初始化时创建为深灰色
        'info_text_color': None,  # 信息文本颜色，将在初始化时创建为白色
        # 字体垂直偏移设置已移除，现在使用Qt的自动居中文本
        # 添加字体相关设置
        'font_family': '',         # 字体名称
        'font_style': '常规',      # 字体样式
        'font_style_code': 0,      # 字体样式代码
        'font_underline': False,   # 下划线
        'font_strikeout': False,   # 删除线
    }
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("预览图设置")
        self.setModal(True)
        self.parent_player = parent  # 保存父窗口引用
        self.resize(600, 700)  # 设置一个更合适的初始大小
        
        # 设置按钮样式表，去掉焦点时的红色边框
        self.setStyleSheet("""
            QPushButton:focus, QPushButton:default {
                border: 1px solid #8f8f91;
                outline: none;
                background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                 stop: 0 #f6f7fa, stop: 1 #dadbde);
            }
            
            QDialogButtonBox QPushButton:focus, QDialogButtonBox QPushButton:default {
                border: 1px solid #8f8f91;
                outline: none;
                background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                 stop: 0 #f6f7fa, stop: 1 #dadbde);
            }
        """)
        
        # 初始化背景图相关属性
        self.background_image_path = ""
        self.has_bg_image_data = False
        self.background_image_data = None
        
        # 如果last_settings中的颜色为None，初始化为默认颜色
        from PyQt6.QtGui import QColor
        if PreviewSettingsDialog.last_settings['bg_color'] is None:
            PreviewSettingsDialog.last_settings['bg_color'] = QColor(235, 235, 235)  # 浅灰色背景
        if PreviewSettingsDialog.last_settings['info_bg_color'] is None:
            PreviewSettingsDialog.last_settings['info_bg_color'] = QColor(50, 50, 50)  # 深灰色背景
        if PreviewSettingsDialog.last_settings['info_text_color'] is None:
            PreviewSettingsDialog.last_settings['info_text_color'] = QColor(255, 255, 255)  # 白色文本
        
        # 保存当前颜色设置
        self.bg_color = PreviewSettingsDialog.last_settings['bg_color']
        self.info_bg_color = PreviewSettingsDialog.last_settings['info_bg_color']
        self.info_text_color = PreviewSettingsDialog.last_settings['info_text_color']
        
        # 创建主布局
        main_layout = QVBoxLayout()
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # 创建内容窗口部件
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)
        
        # 布局设置组
        layout_group = QGroupBox("布局设置")
        layout_form = QFormLayout()
        
        # 行列设置
        self.rows_spin = QSpinBox()
        self.rows_spin.setRange(1, 16)  # 最少2行，支持两行预览
        self.rows_spin.setValue(PreviewSettingsDialog.last_settings['rows'])
        layout_form.addRow("总行数:", self.rows_spin)
        
        self.cols_spin = QSpinBox()
        self.cols_spin.setRange(1, 10)
        self.cols_spin.setValue(PreviewSettingsDialog.last_settings['cols'])
        layout_form.addRow("列数:", self.cols_spin)
        
        # 间距设置
        self.h_spacing_spin = QSpinBox()
        self.h_spacing_spin.setRange(0, 600)
        self.h_spacing_spin.setValue(PreviewSettingsDialog.last_settings['h_spacing'])
        layout_form.addRow("水平间距:", self.h_spacing_spin)
        
        self.v_spacing_spin = QSpinBox()
        self.v_spacing_spin.setRange(0, 600)
        self.v_spacing_spin.setValue(PreviewSettingsDialog.last_settings['v_spacing'])
        layout_form.addRow("垂直间距:", self.v_spacing_spin)
        
        layout_group.setLayout(layout_form)
        layout.addWidget(layout_group)
        
        # 边距设置组
        margin_group = QGroupBox("边距设置")
        margin_form = QFormLayout()
        
        self.top_margin_spin = QSpinBox()
        self.top_margin_spin.setRange(0, 600)
        self.top_margin_spin.setValue(PreviewSettingsDialog.last_settings['top_margin'])
        margin_form.addRow("上边距:", self.top_margin_spin)
        
        self.bottom_margin_spin = QSpinBox()
        self.bottom_margin_spin.setRange(0, 600)
        self.bottom_margin_spin.setValue(PreviewSettingsDialog.last_settings['bottom_margin'])
        margin_form.addRow("下边距:", self.bottom_margin_spin)
        
        self.left_margin_spin = QSpinBox()
        self.left_margin_spin.setRange(0, 600)
        self.left_margin_spin.setValue(PreviewSettingsDialog.last_settings['left_margin'])
        margin_form.addRow("左边距:", self.left_margin_spin)
        
        self.right_margin_spin = QSpinBox()
        self.right_margin_spin.setRange(0, 600)
        self.right_margin_spin.setValue(PreviewSettingsDialog.last_settings['right_margin'])
        margin_form.addRow("右边距:", self.right_margin_spin)
        
        margin_group.setLayout(margin_form)
        layout.addWidget(margin_group)
        
        # 添加背景设置组
        from PyQt6.QtGui import QColor
        from PyQt6.QtWidgets import QColorDialog
        
        bg_group = QGroupBox("背景设置")
        bg_layout = QVBoxLayout()
        
        # 背景颜色选择
        color_layout = QHBoxLayout()
        color_layout.addWidget(QLabel("背景颜色:"))
        
        self.color_btn = QPushButton()
        self.color_btn.setFixedSize(30, 20)
        self.bg_color = PreviewSettingsDialog.last_settings['bg_color']
        self.update_color_button()
        self.color_btn.clicked.connect(self.choose_color)
        color_layout.addWidget(self.color_btn)
        
        # 添加预设颜色按钮
        for color in [QColor(255, 255, 255), QColor(0, 0, 0), QColor(240, 240, 240), 
                     QColor(200, 200, 255), QColor(255, 200, 200)]:
            preset_btn = QPushButton()
            preset_btn.setFixedSize(20, 20)
            preset_btn.setStyleSheet(f"background-color: {color.name()}; border: 1px solid #999;")
            preset_btn.clicked.connect(lambda checked, c=color: self.set_color(c))
            color_layout.addWidget(preset_btn)
        
        color_layout.addStretch()
        bg_layout.addLayout(color_layout)
        
        # 背景图片设置
        bg_img_layout = QHBoxLayout()
        bg_img_layout.addWidget(QLabel("背景图片:"))
        
        self.bg_img_path = QLineEdit()
        self.bg_img_path.setReadOnly(True)
        self.bg_img_path.setPlaceholderText("未选择背景图片")
        if PreviewSettingsDialog.last_settings['bg_image_path']:
            self.bg_img_path.setText(PreviewSettingsDialog.last_settings['bg_image_path'])
            self.background_image_path = PreviewSettingsDialog.last_settings['bg_image_path']
        bg_img_layout.addWidget(self.bg_img_path)
        
        self.browse_btn = QPushButton("浏览...")
        self.browse_btn.clicked.connect(self.browse_background)
        bg_img_layout.addWidget(self.browse_btn)
        
        self.clear_btn = QPushButton("清除")
        self.clear_btn.clicked.connect(self.clear_background)
        bg_img_layout.addWidget(self.clear_btn)
        
        bg_layout.addLayout(bg_img_layout)
        
        # 背景图片选项
        options_layout = QHBoxLayout()
        
        self.use_bg_img_check = QCheckBox("使用背景图片")
        self.use_bg_img_check.setChecked(PreviewSettingsDialog.last_settings['use_bg_image'])
        self.use_bg_img_check.stateChanged.connect(self.on_use_bg_changed)
        options_layout.addWidget(self.use_bg_img_check)
        
        self.tile_bg_check = QCheckBox("平铺背景")
        self.tile_bg_check.setChecked(PreviewSettingsDialog.last_settings['tile_bg'])
        self.tile_bg_check.setEnabled(PreviewSettingsDialog.last_settings['use_bg_image'])
        options_layout.addWidget(self.tile_bg_check)
        
        self.keep_ratio_check = QCheckBox("保持原始比例")
        self.keep_ratio_check.setChecked(PreviewSettingsDialog.last_settings['keep_ratio'])
        self.keep_ratio_check.setEnabled(PreviewSettingsDialog.last_settings['use_bg_image'])
        options_layout.addWidget(self.keep_ratio_check)
        
        bg_layout.addLayout(options_layout)
        
        # 背景透明度
        opacity_layout = QHBoxLayout()
        opacity_layout.addWidget(QLabel("背景透明度:"))
        
        self.opacity_slider = QSlider(Qt.Orientation.Horizontal)
        self.opacity_slider.setRange(0, 100)
        self.opacity_slider.setValue(int(PreviewSettingsDialog.last_settings['bg_opacity'] * 100))
        self.opacity_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.opacity_slider.setTickInterval(10)
        opacity_layout.addWidget(self.opacity_slider)
        
        self.opacity_label = QLabel(f"{int(PreviewSettingsDialog.last_settings['bg_opacity'] * 100)}%")
        self.opacity_slider.valueChanged.connect(
            lambda v: self.opacity_label.setText(f"{v}%"))
        opacity_layout.addWidget(self.opacity_label)
        
        bg_layout.addLayout(opacity_layout)
        
        bg_group.setLayout(bg_layout)
        layout.addWidget(bg_group)
        
        # 预览说明
        info_label = QLabel("预览图将显示视频的缩略图，帮助您快速浏览视频内容")
        info_label.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(info_label)
        
        # 添加导出布局设置组
        export_layout_group = QGroupBox("导出布局设置")
        export_layout_form = QFormLayout()
        
        self.use_custom_export_layout_check = QCheckBox("按设置的布局导出")
        self.use_custom_export_layout_check.setChecked(PreviewSettingsDialog.last_settings['use_custom_export_layout'])
        self.use_custom_export_layout_check.stateChanged.connect(self.on_use_custom_export_layout_changed)
        export_layout_form.addRow(self.use_custom_export_layout_check)
        
        self.export_rows_spin = QSpinBox()
        self.export_rows_spin.setRange(1, 10)
        self.export_rows_spin.setValue(PreviewSettingsDialog.last_settings['export_rows'])
        self.export_rows_spin.setEnabled(PreviewSettingsDialog.last_settings['use_custom_export_layout'])
        export_layout_form.addRow("导出行数:", self.export_rows_spin)
        
        self.export_cols_spin = QSpinBox()
        self.export_cols_spin.setRange(1, 10)
        self.export_cols_spin.setValue(PreviewSettingsDialog.last_settings['export_cols'])
        self.export_cols_spin.setEnabled(PreviewSettingsDialog.last_settings['use_custom_export_layout'])
        export_layout_form.addRow("导出列数:", self.export_cols_spin)
        
        export_layout_group.setLayout(export_layout_form)
        layout.addWidget(export_layout_group)
        
        # 按钮
        buttons = QHBoxLayout()
        
        # 添加应用按钮
        apply_btn = QPushButton("应用")
        apply_btn.clicked.connect(self.apply_settings)
        apply_btn.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
            QPushButton:focus {
                outline: none;
                border: none;
                background-color: #2ecc71;
            }
            QPushButton:default {
                outline: none;
                border: none;
                background-color: #2ecc71;
            }
        """)
        buttons.addWidget(apply_btn)
        
        # 添加测试背景图按钮
        test_bg_btn = QPushButton("测试背景图")
        test_bg_btn.clicked.connect(self.test_background_image)
        buttons.addWidget(test_bg_btn)
        
        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept)
        ok_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1c6ea4;
            }
            QPushButton:focus {
                outline: none;
                border: none;
                background-color: #3498db;
            }
            QPushButton:default {
                outline: none;
                border: none;
                background-color: #3498db;
            }
        """)
        buttons.addWidget(ok_btn)
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        buttons.addWidget(cancel_btn)
        
        layout.addLayout(buttons)
        
        # 添加视频信息显示设置组
        info_group = QGroupBox("视频信息显示")
        info_layout = QVBoxLayout()
        
        # 显示/隐藏视频信息选项
        self.show_info_check = QCheckBox("在预览图上显示视频信息")
        self.show_info_check.setChecked(PreviewSettingsDialog.last_settings.get('show_video_info', True))
        info_layout.addWidget(self.show_info_check)
        
        # 字体设置区域
        font_group = QGroupBox("字体设置")
        font_layout = QGridLayout()
        
        # 字体选择下拉框
        self.font_combo = QFontComboBox()
        if PreviewSettingsDialog.last_settings.get('font_family'):
            self.font_combo.setCurrentFont(QFont(PreviewSettingsDialog.last_settings.get('font_family')))
        font_layout.addWidget(QLabel("字体:"), 0, 0)
        font_layout.addWidget(self.font_combo, 0, 1)
        
        # 字体样式选择
        self.style_combo = QComboBox()
        self.style_combo.addItems(["常规", "粗体", "斜体", "粗斜体"])
        self.style_combo.setCurrentText(PreviewSettingsDialog.last_settings.get('font_style', '常规'))
        font_layout.addWidget(QLabel("字形:"), 0, 2)
        font_layout.addWidget(self.style_combo, 0, 3)
        
        # 字体大小设置 - 使用中文字号
        font_layout.addWidget(QLabel("字体大小:"), 1, 0)
        self.font_size_combo = QComboBox()
        # 添加中文字号
        sizes = ["初号", "小初", "一号", "小一", "二号", "小二", "三号", "四号", "小四", "五号", "小五", "六号", "小六", "七号", "八号"]
        self.font_size_combo.addItems(sizes)
        
        # 获取当前字体大小设置
        current_size = PreviewSettingsDialog.last_settings.get('info_font_size', 16)
        
        # 中文字号与数字的对应关系
        size_map = {
            "初号": 42.0,
            "小初": 36.0,
            "一号": 26.0,
            "小一": 24.0,
            "二号": 22.0,
            "小二": 18.0,
            "三号": 16.0,
            "四号": 14.0,
            "小四": 12.0,
            "五号": 10.5,
            "小五": 9.0,
            "六号": 7.5,
            "小六": 6.5,
            "七号": 5.5,
            "八号": 5.0
        }
        
        # 根据数值找到最接近的中文字号
        if isinstance(current_size, (int, float)):
            closest_size = "三号"  # 默认
            min_diff = float('inf')
            for name, value in size_map.items():
                diff = abs(value - float(current_size))
                if diff < min_diff:
                    min_diff = diff
                    closest_size = name
            self.font_size_combo.setCurrentText(closest_size)
        elif isinstance(current_size, str) and current_size in sizes:
            self.font_size_combo.setCurrentText(current_size)
            
        font_layout.addWidget(self.font_size_combo, 1, 1)
        
        # 效果选项
        self.underline_check = QCheckBox("下划线")
        self.underline_check.setChecked(PreviewSettingsDialog.last_settings.get('font_underline', False))
        font_layout.addWidget(self.underline_check, 1, 2)
        
        self.strikeout_check = QCheckBox("删除线")
        self.strikeout_check.setChecked(PreviewSettingsDialog.last_settings.get('font_strikeout', False))
        font_layout.addWidget(self.strikeout_check, 1, 3)
        
        # 预览区域
        preview_group = QGroupBox("字体预览")
        preview_layout = QVBoxLayout()
        
        # 预览文本输入
        self.preview_input = QLineEdit("AaBbYyZz 你好，世界！")
        self.preview_input.textChanged.connect(self.update_font_preview)
        preview_layout.addWidget(self.preview_input)
        
        # 使用自定义预览组件
        self.preview_text = CustomFontPreview()
        preview_layout.addWidget(self.preview_text)
        
        preview_group.setLayout(preview_layout)
        font_layout.addWidget(preview_group, 2, 0, 1, 4)
        
        font_group.setLayout(font_layout)
        info_layout.addWidget(font_group)
        
        # 初始化字体预览
        QTimer.singleShot(100, self.update_font_preview)
        
        # 字体垂直偏移设置已移除，现在使用Qt的自动居中文本
        
        # 信息位置设置
        info_pos_layout = QHBoxLayout()
        info_pos_layout.addWidget(QLabel("信息位置:"))
        self.info_pos_combo = QComboBox()
        self.info_pos_combo.addItems(["顶部", "底部"])
        current_pos = PreviewSettingsDialog.last_settings.get('info_position', 'top')
        self.info_pos_combo.setCurrentText("顶部" if current_pos == 'top' else "底部")
        info_pos_layout.addWidget(self.info_pos_combo)
        info_layout.addLayout(info_pos_layout)
        
        # 添加信息背景颜色选择
        info_bg_color_layout = QHBoxLayout()
        info_bg_color_layout.addWidget(QLabel("信息背景颜色:"))
        
        self.info_bg_color_btn = QPushButton()
        self.info_bg_color_btn.setFixedSize(30, 20)
        self.update_info_bg_color_button()
        self.info_bg_color_btn.clicked.connect(self.choose_info_bg_color)
        info_bg_color_layout.addWidget(self.info_bg_color_btn)
        
        # 添加预设背景颜色按钮
        for color in [QColor(0, 0, 0), QColor(50, 50, 50), QColor(30, 30, 80), 
                     QColor(80, 30, 30), QColor(30, 80, 30)]:
            preset_btn = QPushButton()
            preset_btn.setFixedSize(20, 20)
            preset_btn.setStyleSheet(f"background-color: {color.name()}; border: 1px solid #999;")
            preset_btn.clicked.connect(lambda checked, c=color: self.set_info_bg_color(c))
            info_bg_color_layout.addWidget(preset_btn)
        
        info_bg_color_layout.addStretch()
        info_layout.addLayout(info_bg_color_layout)
        
        # 添加信息文本颜色选择
        info_text_color_layout = QHBoxLayout()
        info_text_color_layout.addWidget(QLabel("信息文本颜色:"))
        
        self.info_text_color_btn = QPushButton()
        self.info_text_color_btn.setFixedSize(30, 20)
        self.update_info_text_color_button()
        self.info_text_color_btn.clicked.connect(self.choose_info_text_color)
        info_text_color_layout.addWidget(self.info_text_color_btn)
        
        # 添加预设文本颜色按钮
        for color in [QColor(255, 255, 255), QColor(200, 200, 200), QColor(255, 255, 0), 
                     QColor(0, 255, 255), QColor(255, 200, 200)]:
            preset_btn = QPushButton()
            preset_btn.setFixedSize(20, 20)
            preset_btn.setStyleSheet(f"background-color: {color.name()}; border: 1px solid #999;")
            preset_btn.clicked.connect(lambda checked, c=color: self.set_info_text_color(c))
            info_text_color_layout.addWidget(preset_btn)
        
        info_text_color_layout.addStretch()
        info_layout.addLayout(info_text_color_layout)
        
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # 设置滚动区域的内容
        scroll_area.setWidget(content_widget)
        
        # 将滚动区域添加到主布局
        main_layout.addWidget(scroll_area)
        
        # 设置主布局
        self.setLayout(main_layout)
        
        # 连接预览信号
        self.connect_preview_signals()
        
        # 添加事件过滤器来监控控件状态变化
        self.export_rows_spin.installEventFilter(self)
        self.export_cols_spin.installEventFilter(self)
        self.use_custom_export_layout_check.installEventFilter(self)
        
        # 打印初始状态
        print("=== 初始化预览设置对话框 ===")
        print(f"初始导出布局勾选状态: {self.use_custom_export_layout_check.isChecked()}")
        print(f"初始导出行数控件状态: 启用={self.export_rows_spin.isEnabled()}, 可读={not self.export_rows_spin.isReadOnly()}")
        print(f"初始导出列数控件状态: 启用={self.export_cols_spin.isEnabled()}, 可读={not self.export_cols_spin.isReadOnly()}")
    
    def update_color_button(self):
        """更新颜色按钮的显示"""
        self.color_btn.setStyleSheet(
            f"background-color: {self.bg_color.name()}; border: 1px solid #999;")
    
    def choose_color(self):
        """选择背景颜色"""
        from PyQt6.QtWidgets import QColorDialog
        color = QColorDialog.getColor(self.bg_color, self, "选择背景颜色")
        if color.isValid():
            self.bg_color = color
            self.update_color_button()
            self.on_setting_changed()
    
    def set_color(self, color):
        """设置预设颜色"""
        self.bg_color = color
        self.update_color_button()
        self.on_setting_changed()
    
    # 添加信息背景颜色相关方法
    def update_info_bg_color_button(self):
        """更新信息背景颜色按钮的显示"""
        self.info_bg_color_btn.setStyleSheet(
            f"background-color: {self.info_bg_color.name()}; border: 1px solid #999;")
    
    def choose_info_bg_color(self):
        """选择信息背景颜色"""
        from PyQt6.QtWidgets import QColorDialog
        color = QColorDialog.getColor(self.info_bg_color, self, "选择信息背景颜色")
        if color.isValid():
            self.info_bg_color = color
            self.update_info_bg_color_button()
            self.on_setting_changed()
    
    def set_info_bg_color(self, color):
        """设置预设信息背景颜色"""
        self.info_bg_color = color
        self.update_info_bg_color_button()
        self.on_setting_changed()
    
    # 添加信息文本颜色相关方法
    def update_info_text_color_button(self):
        """更新信息文本颜色按钮的显示"""
        self.info_text_color_btn.setStyleSheet(
            f"background-color: {self.info_text_color.name()}; border: 1px solid #999;")
    
    def choose_info_text_color(self):
        """选择信息文本颜色"""
        from PyQt6.QtWidgets import QColorDialog
        color = QColorDialog.getColor(self.info_text_color, self, "选择信息文本颜色")
        if color.isValid():
            # 直接使用选择的颜色，不再交换通道
            self.info_text_color = color
            self.update_info_text_color_button()
            self.on_setting_changed()
    
    def set_info_text_color(self, color):
        """设置预设信息文本颜色"""
        # 直接使用颜色，不再交换通道
        self.info_text_color = color
        self.update_info_text_color_button()
        self.on_setting_changed()
    
    def connect_preview_signals(self):
        """连接预览更新信号"""
        # 连接所有可能影响预览的控件信号
        self.rows_spin.valueChanged.connect(self.on_setting_changed)
        self.cols_spin.valueChanged.connect(self.on_setting_changed)
        self.h_spacing_spin.valueChanged.connect(self.on_setting_changed)
        self.v_spacing_spin.valueChanged.connect(self.on_setting_changed)
        self.top_margin_spin.valueChanged.connect(self.on_setting_changed)
        self.bottom_margin_spin.valueChanged.connect(self.on_setting_changed)
        self.left_margin_spin.valueChanged.connect(self.on_setting_changed)
        self.right_margin_spin.valueChanged.connect(self.on_setting_changed)
        self.opacity_slider.valueChanged.connect(self.on_setting_changed)
        self.tile_bg_check.stateChanged.connect(self.on_setting_changed)
        self.keep_ratio_check.stateChanged.connect(self.on_setting_changed)
        self.export_rows_spin.valueChanged.connect(self.on_setting_changed)
        self.export_cols_spin.valueChanged.connect(self.on_setting_changed)
        
        # 添加新的信号连接
        self.show_info_check.stateChanged.connect(self.on_setting_changed)
        self.font_size_combo.currentTextChanged.connect(self.on_setting_changed)
        self.info_pos_combo.currentTextChanged.connect(self.on_setting_changed)
        
        # 连接字体设置相关信号
        self.font_combo.currentFontChanged.connect(self.on_setting_changed)
        self.style_combo.currentTextChanged.connect(self.on_setting_changed)
        self.underline_check.stateChanged.connect(self.on_setting_changed)
        self.strikeout_check.stateChanged.connect(self.on_setting_changed)
        
        # 字体偏移控制信号已移除，现在使用Qt的自动居中文本
    
    def disconnect_preview_signals(self):
        """断开预览更新信号"""
        try:
            self.rows_spin.valueChanged.disconnect(self.on_setting_changed)
            self.cols_spin.valueChanged.disconnect(self.on_setting_changed)
            self.h_spacing_spin.valueChanged.disconnect(self.on_setting_changed)
            self.v_spacing_spin.valueChanged.disconnect(self.on_setting_changed)
            self.top_margin_spin.valueChanged.disconnect(self.on_setting_changed)
            self.bottom_margin_spin.valueChanged.disconnect(self.on_setting_changed)
            self.left_margin_spin.valueChanged.disconnect(self.on_setting_changed)
            self.right_margin_spin.valueChanged.disconnect(self.on_setting_changed)
            self.opacity_slider.valueChanged.disconnect(self.on_setting_changed)
            self.tile_bg_check.stateChanged.disconnect(self.on_setting_changed)
            self.keep_ratio_check.stateChanged.disconnect(self.on_setting_changed)
            self.export_rows_spin.valueChanged.disconnect(self.on_setting_changed)
            self.export_cols_spin.valueChanged.disconnect(self.on_setting_changed)
            self.show_info_check.stateChanged.disconnect(self.on_setting_changed)
            self.font_size_combo.currentTextChanged.disconnect(self.on_setting_changed)
            self.info_pos_combo.currentTextChanged.disconnect(self.on_setting_changed)
            
            # 断开字体设置相关信号
            self.font_combo.currentFontChanged.disconnect(self.on_setting_changed)
            self.style_combo.currentTextChanged.disconnect(self.on_setting_changed)
            self.underline_check.stateChanged.disconnect(self.on_setting_changed)
            self.strikeout_check.stateChanged.disconnect(self.on_setting_changed)
            
            # 字体偏移控制信号已移除，现在使用Qt的自动居中文本
        except:
            # 忽略断开信号时的错误
            pass
    
    def on_setting_changed(self):
        """设置变化时更新预览"""
        if self.parent_player and self.parent_player.decoder:
            # 使用延迟更新，避免频繁重绘
            if hasattr(self, 'update_timer'):
                self.update_timer.stop()
            else:
                self.update_timer = QTimer()
                self.update_timer.setSingleShot(True)
                self.update_timer.timeout.connect(self.apply_settings)
            
            # 设置300ms的延迟
            self.update_timer.start(300)
            
        # 更新字体预览
        if hasattr(self, 'preview_text'):
            self.update_font_preview()
    
    def update_font_preview(self):
        """更新字体预览"""
        if not hasattr(self, 'preview_text'):
            return
        
        # 获取当前字体设置
        font = self.font_combo.currentFont()
        
        # 设置字体大小 - 使用中文字号映射表
        font_size_name = self.font_size_combo.currentText()
        
        # 中文字号映射表
        size_map = {
            "初号": 42.0,
            "小初": 36.0,
            "一号": 26.0,
            "小一": 24.0,
            "二号": 22.0,
            "小二": 18.0,
            "三号": 16.0,
            "四号": 14.0,
            "小四": 12.0,
            "五号": 10.5,
            "小五": 9.0,
            "六号": 7.5,
            "小六": 6.5,
            "七号": 5.5,
            "八号": 5.0
        }
        
        # 获取对应的大小值
        font_size = size_map.get(font_size_name, 16.0)
        
        # 使用浮点数设置字体大小
        font.setPointSizeF(font_size)
        
        # 设置字体平滑和抗锯齿
        font.setStyleStrategy(QFont.StyleStrategy.PreferAntialias)
        font.setHintingPreference(QFont.HintingPreference.PreferFullHinting)
        
        # 设置字体样式
        style = self.style_combo.currentText()
        if style == "粗体":
            font.setWeight(QFont.Weight.Bold)
            font.setItalic(False)
        elif style == "斜体":
            font.setWeight(QFont.Weight.Normal)
            font.setItalic(True)
        elif style == "粗斜体":
            font.setWeight(QFont.Weight.Bold)
            font.setItalic(True)
        else:  # 常规
            font.setWeight(QFont.Weight.Normal)
            font.setItalic(False)
        
        # 设置下划线和删除线
        font.setUnderline(self.underline_check.isChecked())
        font.setStrikeOut(self.strikeout_check.isChecked())
        
        # 更新预览文本
        if hasattr(self, 'preview_input'):
            self.preview_text.setText(self.preview_input.text())
        self.preview_text.setFont(font)
        
        # 设置颜色
        self.preview_text.setColors(self.info_text_color, self.info_bg_color)
    
    def apply_settings(self):
        """应用当前设置"""
        if self.parent_player and self.parent_player.decoder:
            settings = self.get_settings()
            self.parent_player.generate_preview(settings)
    
    def get_settings(self):
        """获取当前设置"""
        settings = {
            'rows': self.rows_spin.value(),
            'cols': self.cols_spin.value(),
            'h_spacing': self.h_spacing_spin.value(),
            'v_spacing': self.v_spacing_spin.value(),
            'top_margin': self.top_margin_spin.value(),
            'bottom_margin': self.bottom_margin_spin.value(),
            'left_margin': self.left_margin_spin.value(),
            'right_margin': self.right_margin_spin.value(),
            'bg_color': self.bg_color,
            'use_bg_image': self.use_bg_img_check.isChecked(),
            'bg_image_path': self.background_image_path,
            'tile_bg': self.tile_bg_check.isChecked(),
            'keep_ratio': self.keep_ratio_check.isChecked(),
            'bg_opacity': self.opacity_slider.value() / 100.0,
            'use_custom_export_layout': self.use_custom_export_layout_check.isChecked(),
            'export_rows': self.export_rows_spin.value(),
            'export_cols': self.export_cols_spin.value(),
            # 添加视频信息设置
            'show_video_info': self.show_info_check.isChecked(),
            'info_font_size': self.font_size_combo.currentText(),  # 保存中文字号名称
            'info_position': 'top' if self.info_pos_combo.currentText() == "顶部" else 'bottom',
            # 添加视频信息颜色设置
            'info_bg_color': self.info_bg_color,
            'info_text_color': self.info_text_color,
            # 字体垂直偏移设置已移除，现在使用Qt的自动居中文本
            # 添加字体相关设置
            'font_family': self.font_combo.currentFont().family(),
            'font_style': self.style_combo.currentText(),
            'font_style_code': self.style_combo.currentIndex(),
            'font_underline': self.underline_check.isChecked(),
            'font_strikeout': self.strikeout_check.isChecked(),
        }
        
        # 如果有内存中的图像数据，也包含它
        if self.has_bg_image_data and self.background_image_data is not None:
            settings['bg_image_data'] = self.background_image_data
        
        return settings
    
    def accept(self):
        """当用户点击确定按钮时调用"""
        # 更新 last_settings
        settings = self.get_settings()
        for key, value in settings.items():
            PreviewSettingsDialog.last_settings[key] = value
        
        # 保存设置到文件
        if self.parent_player:
            self.parent_player.save_preview_settings()
        
        # 如果有父窗口，立即更新预览
        if self.parent_player:
            self.parent_player.update_preview_now()
        
        # 确保控件状态正确
        self.on_use_bg_changed(Qt.CheckState.Checked if self.use_bg_img_check.isChecked() else Qt.CheckState.Unchecked)
        self.on_use_custom_export_layout_changed(Qt.CheckState.Checked if self.use_custom_export_layout_check.isChecked() else Qt.CheckState.Unchecked)
        
        super().accept()

    def reject(self):
        """当用户点击取消按钮时调用"""
        # 如果有父窗口，使用 last_settings 更新预览
        if self.parent_player:
            self.parent_player.update_preview_now()
        
        # 确保控件状态正确
        self.on_use_bg_changed(Qt.CheckState.Checked if self.use_bg_img_check.isChecked() else Qt.CheckState.Unchecked)
        self.on_use_custom_export_layout_changed(Qt.CheckState.Checked if self.use_custom_export_layout_check.isChecked() else Qt.CheckState.Unchecked)
        
        super().reject()    
    def test_background_image(self):
        """测试背景图片是否可以被正确读取和显示"""
        if self.has_bg_image_data and self.background_image_data is not None:
            # 使用PyQt显示图像
            self.show_image_preview(self.background_image_data, "内存中的背景图像")
            return
            
        if not self.background_image_path:
            QMessageBox.information(self, "提示", "请先选择背景图片")
            return
            
        # 检查文件是否存在
        if not os.path.exists(self.background_image_path):
            QMessageBox.warning(self, "错误", f"文件不存在: {self.background_image_path}")
            return
            
        try:
            # 使用新的函数读取图片
            bg_img = cv2_imread_chinese(self.background_image_path)
            if bg_img is None:
                raise Exception("无法读取图片文件")
                
            # 保存到内存
            self.background_image_data = bg_img
            self.has_bg_image_data = True
            
            QMessageBox.information(self, "提示", f"成功读取图片，尺寸: {bg_img.shape}")
            
            # 显示图像
            self.show_image_preview(bg_img, "读取的背景图像")
            return
        except Exception as e:
            QMessageBox.warning(self, "错误", f"读取图片失败: {str(e)}")

    def show_image_preview(self, img_data, title="图像预览"):
        """使用PyQt显示图像预览"""
        try:
            # 转换为RGB
            rgb_img = cv2.cvtColor(img_data, cv2.COLOR_BGR2RGB)
            h, w, c = rgb_img.shape
            
            # 创建QImage
            q_img = QImage(rgb_img.data, w, h, w * c, QImage.Format.Format_RGB888)
            
            # 创建QPixmap
            pixmap = QPixmap.fromImage(q_img)
            
            # 创建预览对话框
            preview = QDialog(self)
            preview.setWindowTitle(title)
            
            # 根据图片尺寸设置对话框大小，但设置一个最小值
            dialog_width = max(w, 320)
            dialog_height = max(h, 240)
            # 限制最大尺寸，避免图片太大时对话框超出屏幕
            max_width = 1200
            max_height = 800
            if dialog_width > max_width or dialog_height > max_height:
                # 如果图片尺寸超过最大限制，按比例缩小
                scale = min(max_width / dialog_width, max_height / dialog_height)
                dialog_width = int(dialog_width * scale)
                dialog_height = int(dialog_height * scale)
            
            preview.resize(dialog_width + 40, dialog_height + 60)  # 添加一些边距
            
            # 创建布局
            layout = QVBoxLayout()
            
            # 创建标签显示图像
            img_label = QLabel()
            # 使用原始尺寸显示图片，不进行缩放
            img_label.setPixmap(pixmap)
            img_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            
            # 创建滚动区域，以便在图片大于窗口时可以滚动查看
            scroll_area = QScrollArea()
            scroll_area.setWidget(img_label)
            scroll_area.setWidgetResizable(True)
            
            # 添加到布局
            layout.addWidget(scroll_area)
            
            # 添加关闭按钮
            close_btn = QPushButton("关闭")
            close_btn.clicked.connect(preview.accept)
            layout.addWidget(close_btn)
            
            # 设置布局
            preview.setLayout(layout)
            
            # 显示对话框
            preview.exec()
        except Exception as e:
            QMessageBox.warning(self, "错误", f"无法显示图像预览: {str(e)}")

    def eventFilter(self, obj, event):
        """事件过滤器，用于监控控件状态变化"""
        if event.type() == QEvent.Type.EnabledChange:
            if obj == self.export_rows_spin:
                print(f"导出行数控件启用状态变化: {obj.isEnabled()}")
            elif obj == self.export_cols_spin:
                print(f"导出列数控件启用状态变化: {obj.isEnabled()}")
            elif obj == self.use_custom_export_layout_check:
                print(f"导出布局勾选框启用状态变化: {obj.isEnabled()}")
        
        return super().eventFilter(obj, event)

    def recreate_export_controls(self):
        """重新创建导出控件"""
        print("重新创建导出控件")
        
        # 获取当前值
        current_rows = self.export_rows_spin.value()
        current_cols = self.export_cols_spin.value()
        enabled = self.use_custom_export_layout_check.isChecked()
        
        # 从布局中移除旧控件
        layout = self.export_rows_spin.parent().layout()
        layout.removeWidget(self.export_rows_spin)
        layout.removeWidget(self.export_cols_spin)
        
        # 删除旧控件
        self.export_rows_spin.deleteLater()
        self.export_cols_spin.deleteLater()
        
        # 创建新控件
        self.export_rows_spin = QSpinBox()
        self.export_rows_spin.setRange(1, 10)
        self.export_rows_spin.setValue(current_rows)
        self.export_rows_spin.setEnabled(enabled)
        
        self.export_cols_spin = QSpinBox()
        self.export_cols_spin.setRange(1, 10)
        self.export_cols_spin.setValue(current_cols)
        self.export_cols_spin.setEnabled(enabled)
        
        # 重新添加到布局
        # 注意：这里需要根据你的实际布局结构调整
        # 假设是FormLayout，行标签是"导出行数:"，列标签是"导出列数:"
        form_layout = layout
        for i in range(form_layout.rowCount()):
            label = form_layout.itemAt(i, QFormLayout.ItemRole.LabelRole)
            if label and label.widget() and label.widget().text() == "导出行数:":
                form_layout.setWidget(i, QFormLayout.ItemRole.FieldRole, self.export_rows_spin)
            elif label and label.widget() and label.widget().text() == "导出列数:":
                form_layout.setWidget(i, QFormLayout.ItemRole.FieldRole, self.export_cols_spin)
        
        # 重新连接信号
        self.export_rows_spin.valueChanged.connect(self.on_setting_changed)
        self.export_cols_spin.valueChanged.connect(self.on_setting_changed)
        
        print(f"重建后行数控件状态: 启用={self.export_rows_spin.isEnabled()}")
        print(f"重建后列数控件状态: 启用={self.export_cols_spin.isEnabled()}")

    def recreate_background_controls(self):
        """重新创建背景图相关控件"""
        print("重新创建背景图相关控件")
        
        # 获取当前值和状态
        current_tile_bg = self.tile_bg_check.isChecked()
        current_keep_ratio = self.keep_ratio_check.isChecked()
        current_opacity = self.opacity_slider.value()
        current_bg_path = self.bg_img_path.text()
        enabled = self.use_bg_img_check.isChecked()
        
        # 获取父布局
        parent_layout = self.tile_bg_check.parent().layout()
        
        # 从布局中移除旧控件
        parent_layout.removeWidget(self.tile_bg_check)
        parent_layout.removeWidget(self.keep_ratio_check)
        
        # 删除旧控件
        self.tile_bg_check.deleteLater()
        self.keep_ratio_check.deleteLater()
        
        # 创建新的复选框
        self.tile_bg_check = QCheckBox("平铺背景")
        self.tile_bg_check.setChecked(current_tile_bg)
        self.tile_bg_check.setEnabled(enabled)
        
        self.keep_ratio_check = QCheckBox("保持原始比例")
        self.keep_ratio_check.setChecked(current_keep_ratio)
        self.keep_ratio_check.setEnabled(enabled)
        
        # 重新添加到布局
        # 这里需要根据你的实际布局结构调整
        options_layout = parent_layout
        options_layout.addWidget(self.tile_bg_check)
        options_layout.addWidget(self.keep_ratio_check)
        
        # 重新创建透明度滑块
        opacity_parent = self.opacity_slider.parent().layout()
        opacity_parent.removeWidget(self.opacity_slider)
        opacity_parent.removeWidget(self.opacity_label)
        
        self.opacity_slider.deleteLater()
        self.opacity_label.deleteLater()
        
        self.opacity_slider = QSlider(Qt.Orientation.Horizontal)
        self.opacity_slider.setRange(0, 100)
        self.opacity_slider.setValue(current_opacity)
        self.opacity_slider.setEnabled(enabled)
        self.opacity_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.opacity_slider.setTickInterval(10)
        
        self.opacity_label = QLabel(f"{current_opacity}%")
        self.opacity_label.setEnabled(enabled)
        
        # 重新添加到布局
        opacity_parent.addWidget(self.opacity_slider)
        opacity_parent.addWidget(self.opacity_label)
        
        # 重新连接信号
        self.tile_bg_check.stateChanged.connect(self.on_setting_changed)
        self.keep_ratio_check.stateChanged.connect(self.on_setting_changed)
        self.opacity_slider.valueChanged.connect(
            lambda v: (self.opacity_label.setText(f"{v}%"), self.on_setting_changed()))
        
        print(f"重建后平铺背景状态: 启用={self.tile_bg_check.isEnabled()}, 勾选={self.tile_bg_check.isChecked()}")
        print(f"重建后保持比例状态: 启用={self.keep_ratio_check.isEnabled()}, 勾选={self.keep_ratio_check.isChecked()}")
        print(f"重建后透明度滑块状态: 启用={self.opacity_slider.isEnabled()}, 值={self.opacity_slider.value()}")

    def browse_background(self):
        """浏览选择背景图片"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择背景图片", "", "图片文件 (*.png *.jpg *.jpeg *.bmp)")
        
        if file_path:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                QMessageBox.warning(self, "错误", f"文件不存在: {file_path}")
                return
            
            try:
                # 使用新的函数读取图片
                bg_img = cv2_imread_chinese(file_path)
                if bg_img is None:
                    raise Exception("无法读取图片文件")
                    
                # 保存到内存
                self.background_image_data = bg_img
                self.has_bg_image_data = True
                
                # 保存路径用于显示
                self.background_image_path = file_path
                self.bg_img_path.setText(file_path)
                
                print(f"成功加载背景图到内存: {file_path}, 尺寸: {bg_img.shape}")
                
            except Exception as e:
                self.has_bg_image_data = False
                self.background_image_path = file_path
                self.bg_img_path.setText(file_path)
                print(f"无法处理图片: {str(e)}")
                QMessageBox.warning(self, "警告", f"无法加载图片: {str(e)}")
            
            # 更新UI状态
            self.use_bg_img_check.setChecked(True)
            self.on_use_bg_changed(Qt.CheckState.Checked)
            self.on_setting_changed()
    
    def clear_background(self):
        """清除背景图片"""
        self.background_image_path = ""
        self.bg_img_path.clear()
        self.has_bg_image_data = False
        self.background_image_data = None
        self.use_bg_img_check.setChecked(False)
        self.on_use_bg_changed(Qt.CheckState.Unchecked)
        self.on_setting_changed()
    
    def on_use_bg_changed(self, state):
        """背景图使用状态变化"""
        print(f"\n=== 背景图使用状态变化 ===")
        print(f"状态值: {state}")
        
        # 重建背景相关控件
        self.recreate_background_controls()
        
        # 触发设置变更
        self.on_setting_changed()
    
    def on_use_custom_export_layout_changed(self, state):
        """自定义导出布局使用状态变化"""
        print(f"\n=== 导出布局状态变化 ===")
        print(f"状态值: {state}")
        
        # 尝试重建控件
        self.recreate_export_controls()
        
        # 触发设置变更
        self.on_setting_changed()

    def update_frame(self, position=None):
        """更新当前帧显示"""
        if not self.decoder:
            return
        
        if position is None:
            position = self.slider.value()
        
        # 使用解码器获取帧
        frame = self.decoder.seek_frame(position)
        if frame is not None:
            self.current_frame = frame
            self.display_frame(frame)
            self.update_time_display(position)
            
            # 如果预览图已经生成，更新预览图中的当前位置指示
            if hasattr(self, 'preview_frame_positions') and self.preview_frame_positions:
                self.update_preview_indicator(position)

    def generate_preview(self, settings=None):
        """生成视频预览图"""
        if not self.decoder or not self.video_path:
            return
        
        try:
            # 使用默认设置或传入的设置
            if settings is None:
                settings = PreviewSettingsDialog.last_settings
                
            # 获取视频总帧数和帧率
            total_frames = self.total_frames
            fps = self.fps
            
            if total_frames <= 0 or fps <= 0:
                raise Exception("无法获取视频信息")
                
            # 计算预览帧的位置
            rows = settings.get('rows', 4)
            cols = settings.get('cols', 4)
            total_cells = rows * cols
            
            # 计算帧间隔，确保覆盖整个视频
            frame_interval = max(1, total_frames // total_cells)
            
            # 生成预览帧位置列表
            frame_positions = []
            for i in range(total_cells):
                pos = min(i * frame_interval, total_frames - 1)
                if pos >= 0:
                    frame_positions.append(pos)
                    
            # 保存帧位置信息
            self.preview_frame_positions = frame_positions
            self.current_preview_frames = frame_positions.copy()
            
            # 显示加载进度对话框
            progress = QProgressDialog("正在生成预览图...", "取消", 0, len(frame_positions), self)
            progress.setWindowModality(Qt.WindowModality.WindowModal)
            progress.setMinimumDuration(500)  # 只有操作超过500ms才显示
            
            # 读取所有预览帧
            frames = []
            for i, pos in enumerate(frame_positions):
                if progress.wasCanceled():
                    return
                
                # 使用解码器获取帧
                frame = self.decoder.seek_frame(pos)
                if frame is not None:
                    frames.append(frame)
                
                progress.setValue(i + 1)
                QApplication.processEvents()  # 保持UI响应
                
            # 关闭进度对话框
            progress.close()
            
            if not frames:
                raise Exception("无法读取视频帧")
                
            # 创建预览图
            self.create_preview_image(frames, frame_positions, settings)
            
            # 启用保存按钮
            self.save_preview_btn.setEnabled(True)
            self.save_all_frames_btn.setEnabled(True)
            
            # 在生成预览图时启用恢复按钮
            self.restore_frames_btn.setEnabled(True)
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"生成预览图失败: {str(e)}")
            print(f"预览图生成错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def create_preview_image(self, frames, positions, settings):
        """创建预览图像"""
        try:
            rows = settings.get('rows', 4)
            cols = settings.get('cols', 4)
            h_spacing = settings.get('h_spacing', 10)
            v_spacing = settings.get('v_spacing', 10)
            top_margin = settings.get('top_margin', 20)
            bottom_margin = settings.get('bottom_margin', 20)
            left_margin = settings.get('left_margin', 20)
            right_margin = settings.get('right_margin', 20)
            bg_color = settings.get('bg_color', QColor(255, 255, 255))
            
            # 获取第一帧的尺寸作为参考
            if not frames:
                raise Exception("没有有效的帧")
                
            ref_frame = frames[0]
            frame_h, frame_w = ref_frame.shape[:2]
            
            # 计算缩略图大小
            thumb_w = (800 - left_margin - right_margin - (cols-1) * h_spacing) // cols
            thumb_h = int(thumb_w * frame_h / frame_w)
            
            # 计算总图像大小
            total_width = left_margin + cols * thumb_w + (cols-1) * h_spacing + right_margin
            total_height = top_margin + rows * thumb_h + (rows-1) * v_spacing + bottom_margin
            
            # 创建背景图像
            bg_img = np.zeros((total_height, total_width, 3), dtype=np.uint8)
            bg_img[:, :] = (bg_color.blue(), bg_color.green(), bg_color.red())  # OpenCV使用BGR
            
            # 不添加边框效果
            
            # 放置缩略图
            for i, frame in enumerate(frames):
                if i >= rows * cols:
                    break
                    
                row = i // cols
                col = i % cols
                
                x = left_margin + col * (thumb_w + h_spacing)
                y = top_margin + row * (thumb_h + v_spacing)
                
                # 调整帧大小
                thumb = cv2.resize(frame, (thumb_w, thumb_h), interpolation=cv2.INTER_AREA)
                
                # 放置到背景图上
                bg_img[y:y+thumb_h, x:x+thumb_w] = thumb
                
                # 添加帧号
                frame_pos = positions[i]
                time_str = self.frame_to_timestamp(frame_pos)
                cv2.putText(bg_img, f"{time_str}", (x+5, y+20), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 2, cv2.LINE_AA)
                cv2.putText(bg_img, f"{time_str}", (x+5, y+20), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1, cv2.LINE_AA)
                
            # 修改背景图片处理部分
            if settings.get('use_bg_image', False) and (
                ('bg_image_data' in settings and settings['bg_image_data'] is not None) or 
                (settings.get('bg_image_path', '') and os.path.exists(settings['bg_image_path']))
            ):
                # 使用背景图片
                bg_img = None
                if 'bg_image_data' in settings and settings['bg_image_data'] is not None:
                    # 直接使用已经正确转换的BGR格式数据
                    bg_img = settings['bg_image_data'].copy()
                    print("使用内存中的图像数据作为背景")
                else:
                    try:
                        # 使用新的函数读取图片
                        bg_img = cv2_imread_chinese(settings['bg_image_path'])
                        if bg_img is None:
                            raise Exception("无法读取背景图片")
                        print(f"使用OpenCV读取背景图: {settings['bg_image_path']}")
                    except Exception as e:
                        print(f"加载背景图失败: {str(e)}")
                        bg_img = None

                if bg_img is not None:
                    # 处理背景图
                    if settings.get('tile_bg', True):
                        # 平铺背景
                        bg_h, bg_w = bg_img.shape[:2]
                        tile_img = np.zeros((total_height, total_width, 3), dtype=np.uint8)
                        for y in range(0, total_height, bg_h):
                            for x in range(0, total_width, bg_w):
                                y_end = min(y + bg_h, total_height)
                                x_end = min(x + bg_w, total_width)
                                tile_img[y:y_end, x:x_end] = bg_img[:y_end-y, :x_end-x]
                        bg_img = tile_img
                    else:
                        # 缩放背景
                        if settings.get('keep_ratio', True):
                            # 保持比例
                            bg_h, bg_w = bg_img.shape[:2]
                            ratio = min(total_width / bg_w, total_height / bg_h)
                            new_w = int(bg_w * ratio)
                            new_h = int(bg_h * ratio)
                            bg_img = cv2.resize(bg_img, (new_w, new_h), interpolation=cv2.INTER_AREA)
                            
                            # 居中放置
                            y_offset = (total_height - new_h) // 2
                            x_offset = (total_width - new_w) // 2
                            bg_img[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = bg_img
                        else:
                            # 拉伸填充
                            bg_img = cv2.resize(bg_img, (total_width, total_height), interpolation=cv2.INTER_AREA)
                
                    # 应用透明度
                    opacity = settings.get('bg_opacity', 1.0)
                    if opacity < 1.0:
                        bg_color = settings.get('bg_color', None)
                        if bg_color:
                            # 创建纯色背景
                            color_bg = np.zeros_like(bg_img)
                            color_bg[:] = (bg_color.blue(), bg_color.green(), bg_color.red())
                            # 混合背景
                            bg_img = cv2.addWeighted(bg_img, opacity, color_bg, 1.0 - opacity, 0)

            # 转换为QImage并显示（注意：bg_img已经是BGR格式，需要交换通道）
            h, w = bg_img.shape[:2]
            bytes_per_line = 3 * w
            # 由于输入是BGR格式，这里使用rgbSwapped()来正确显示颜色
            q_img = QImage(bg_img.data, w, h, bytes_per_line, QImage.Format.Format_RGB888).rgbSwapped()
            pixmap = QPixmap.fromImage(q_img)
            
            # 保存预览图像
            self.preview_image = pixmap
            self.preview_area.setPixmap(pixmap)
            
            # 保存帧位置信息
            self.preview_frame_positions = positions
            
            print(f"预览图创建成功: {w}x{h}, {len(frames)}帧")
            
        except Exception as e:
            # 添加异常处理块
            print(f"创建预览图像失败: {str(e)}")
            raise Exception(f"创建预览图像失败: {str(e)}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    player = VideoPlayer()
    player.resize(800, 600)
    player.show()
    sys.exit(app.exec()) 
